import fonts.
/* Base styles */
body {
 margin: 0;
 font-family: "Raleway", sans-serif;
 color: #292b33;
 height: 100%;
 overflow-y: auto;
 background: transparent;
}

.form-header {
 font-family: "Raleway", sans-serif;
 font-weight: 400;
 color: #292b33;

 text-align: center;
 margin: 0;
 p {
  font-size: 20px;
  line-height: 26px;
 }
}

.popup-overlay {
 position: fixed;
 top: 0;
 left: 0;
 width: 100vw;
 height: 100vh;
 background-color: rgba(0, 0, 0, 0.5);
 display: flex;
 justify-content: center;
 align-items: center;
 z-index: 1000;
}

.popup-overlay iframe {
 width: 100%;
 max-width: 1051px;
 height: 100%;
 display: flex;
 justify-content: center;
 align-items: center;
 border: none;
 border-radius: 10px;
 margin: 20px;
}
/* Popup overlay */
.overlays {
 position: fixed;
 top: 0;
 left: 0;
 width: 100vw;
 height: 100vh;
 display: flex;
 justify-content: center;
 align-items: center;
 z-index: 1000;
 padding: 20px;
}

.overlays iframe {
 width: 100%;
 max-width: 1240px;
 height: 100%;
 max-height: 1030px;
 display: flex;
 justify-content: center;
 align-items: center;
 border: none;
 border-radius: 10px;
 background: #fff;
 border: 1.5px solid var(--Dark-Blue, #476477);
 box-shadow: 5px 5px 0px 0px var(--Dark-Blue, #476477);
 border-radius: 5px 60px 5px 5px;
}

/* Form container */
.form-container {
 position: fixed;
 top: 50%;
 left: 50%;
 transform: translate(-50%, -50%);
 width: 100%;
 max-width: 1051px;
 background-color: white;
 padding: 2rem;
 box-sizing: border-box;
 display: flex;
 flex-direction: column;
 border-radius: 10px;
 height: 100%;
}

.close-button {
 position: absolute;
 top: 29px;
 left: 2rem;
 background: none;
 border: none;
 cursor: pointer;
 padding: 0;
 img {
  height: 13px;
  width: 13px;
 }
}

.icon {
 width: 22px;
 height: 22px;
}

.form-content {
 width: 100%;
 display: flex;
 flex-direction: column;
 align-items: center;
 gap: 2.5rem;
 margin-top: 4.125rem;
 overflow-y: auto;
 overflow-x: hidden;
 height: 100%;
 padding-right: 11px;
}

/* Header styles */
.form-header {
 display: flex;
 flex-direction: column;
 align-items: center;
 gap: 0.625rem;
 width: 100%;
}

.form-header h1 {
 font-family: "Playfair Display", serif;
 font-weight: bold;
 font-style: italic;
 color: #292b33;
 font-size: 2.8125rem;
 text-align: center;
 margin: 0;
}

.form-header p {
 font-size: 1rem;
 font-family: "Raleway", sans-serif;
 text-align: center;
 margin: 0;
}

/* Form styles */
.form {
 width: 100%;
 max-width: 951px;
 display: flex;
 flex-direction: column;
 gap: 1.875rem;
}

.form-fields {
 display: flex;
 flex-direction: column;
 gap: 1.875rem;
 width: 100%;
}

.form-row {
 display: flex;
 flex-wrap: wrap;
 gap: 1.25rem;
}

.form-group {
 flex: 1;
 min-width: 300px;
}

.full-width {
 width: 100%;
}

label {
 display: block;
 font-family: "Playfair Display", serif;
 font-weight: normal;
 color: #292b33;
 font-size: 30px;
}

input[type="text"],
input[type="email"] {
 width: 100%;
 border: none;
 border-bottom: 1px solid #292b33;
 border-radius: 0;
 background-color: transparent;
 font-size: 1rem;
}

input[type="text"]:focus,
input[type="email"]:focus {
 outline: none;
 box-shadow: none;
}

.custom-form {
 overflow-x: hidden;
}

.error-message {
 font-family: "Raleway", sans-serif;
 font-weight: 600;
 color: #bd6c6c;
 font-size: 1rem;
 margin: 0;
 display: none;
}

/* Checkbox styles */
.checkbox-group {
 display: flex;
 flex-direction: column;
 gap: 20px;
}

.checkbox-item {
 display: flex;
 align-items: center;
 gap: 1rem;
}

input[type="checkbox"] {
 margin-top: 0.25rem;
 width: 20px;
 height: 20px;
 border-radius: 0;
 border: 1px solid #292b33;
 appearance: none;
 -webkit-appearance: none;
 cursor: pointer;
 position: relative;
 margin: 0;
 padding: 0 10px;
}

input[type="checkbox"]:checked {
 background-color: #292b33;
}

input[type="checkbox"]:checked::after {
 content: "✓";
 color: white;
 position: absolute;
 left: 50%;
 top: 50%;
 transform: translate(-50%, -50%);
}

.checkbox-item label {
 font-family: "Raleway", sans-serif;
 font-weight: normal;
 font-size: 0.875rem;
 cursor: default;
 pointer-events: none;
 line-height: normal;
}

.underline {
 text-decoration: underline;
}

/* Legal text */
.legal-text {
 font-family: "Raleway", sans-serif;
 font-weight: normal;
 font-size: 11px;
 margin-bottom: 0;
}

/* Submit button */
.submit-button-container {
 display: flex;
 justify-content: center;
 margin-top: 1rem;
}

.submit-button {
 width: 179px;
 height: 58px;
 background-color: #81c6b4;
 border: none;
 border-radius: 0;
 font-family: "Raleway", sans-serif;
 font-weight: 600;
 color: white;
 font-size: 1.375rem;
 cursor: pointer;
}

.submit-button:hover {
 background-color: #6db3a1;
}

/* Error states */
.error-input {
 border-bottom-color: #bd6c6c !important;
 color: #bd6c6c;
}

.error-label {
 color: #bd6c6c;
}

.error-checkbox {
 border-color: #bd6c6c !important;
}

@media (max-width: 480px) {
 .form-container {
  padding: 1rem;
 }

 .close-button {
  top: 1rem;
  left: 1rem;
 }

 .form-group {
  min-width: 100%;
 }

 .form-header h1 {
  font-size: 1.75rem;
 }
}

/* No special styles needed since we're removing sections from the DOM */

/* Fix for horizontal scroll sections */
.horizontal-scroll,
.horizontal-scroll2,
.horizontal-scroll3 {
 z-index: 1; /* Ensure scroll container is behind the blur overlay */
}

.pin {
 z-index: 1; /* Ensure pinned elements are behind blur overlay */
}

#access-message {
 position: fixed;
 left: 0;
 right: 0;
 bottom: 0;
 background-color: rgba(0, 0, 0, 0.7);
 color: white;
 padding: 20px;
 z-index: 999;
 text-align: center;
}

.message-content {
 max-width: 600px;
 margin: 0 auto;
}

h3:nth-of-type(1) {
 margin: 0 !important;
}

#access-message h3 {
 font-size: 18px;
}

#unlock-button {
 background-color: #81c6b4;
 color: white;
 border: none;
 padding: 10px 20px;
 cursor: pointer;
 font-weight: bold;
 z-index: 1000;
 position: relative;
 margin-top: 20px;
}
