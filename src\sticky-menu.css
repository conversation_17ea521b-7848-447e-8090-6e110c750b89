/* Sticky Menu Styles */
.sticky-menu {
 position: fixed;
 left: 20px;
 top: 20px;
 z-index: 1000;
 font-family: "Raleway", sans-serif;
 opacity: 0;
 visibility: hidden;
 transition: opacity 0.3s ease, visibility 0.3s ease;
}

.sticky-menu.show {
 opacity: 1;
 visibility: visible;
}

.menu-toggle {
 width: 48px;
 height: 48px;
 border-radius: 15px;
 cursor: pointer;
 display: flex;
 align-items: center;
 justify-content: center;
 position: relative;
 z-index: 10001;
}

.menu-toggle img {
 width: 36px;
 height: 36px;
 object-fit: contain;
}

.menu-content {
 position: fixed;
 left: 85px;
 top: 30px;
 transform: translateX(-115%);
 width: 620px;
 max-width: 90vw;
 height: 90vh;
 max-height: 974px;
 background: #fff;
 transition: transform 0.4s ease-in-out;
 padding: 80px 26px 6px 46px;
 box-shadow: 3px 3px 6.5px rgba(0, 0, 0, 0.25);
 border-radius: 15px;
 overflow-y: auto;
 -webkit-overflow-scrolling: touch;
}

.menu-content.open {
 transform: translateX(0);
}

/* Add style to handle the body when menu is open */
body.menu-open {
 overflow: hidden;
}

.menu-close {
 position: absolute;
 top: 20px;
 right: 25px;
 cursor: pointer;
 z-index: 10001;
 font-size: 27px;
 font-weight: 600;
 color: #292b33;
}

.menu-close img {
 width: 18px;
 height: 18px;
 display: block;
}

.menu-items {
 display: flex;
 flex-direction: column;
 height: calc(100% - 30px);
 overflow-y: auto;
 padding-right: 20px;
 flex: 1;
}

.menu-item {
 margin-bottom: 0;
}

.menu-item-title {
 font-size: 20px;
 font-weight: 400;
 color: #6b6f70;
 cursor: pointer;
 line-height: 1.3;
 position: relative;
 padding: 22px 0;
 border-bottom: 1px solid #6b6f70;
}

/* Main heading - "How to make the Employee Journey count" */
#menu-loyalty-link {
 font-weight: 800;
 color: #292b33;
 font-size: 24px;
 line-height: 1.17;
}

.menu-journey-items {
 display: flex;
 flex-direction: column;
 margin-left: 0;
 margin-top: 5px;
}

.max-w-320 {
 max-width: 320px;
}

.max-w-380 {
 margin-top: 10px;
 max-width: 380px;
}

.menu-journey-item {
 cursor: pointer;
 padding: 22px 0;
 font-size: 20px;
 border-bottom: 1px solid #6b6f70;
}

.journey-item-header {
 display: flex;
 align-items: center;
 gap: 20px;
 width: 100%;
}

.journey-item-number {
 font-size: 20px;
 font-weight: 700;
 color: #000000;
 line-height: 1.3;
 min-width: 30px;
}

.journey-item-title {
 font-size: 20px;
 color: #000000;
 font-weight: 400;
 line-height: 1.3;
}

.journey-item-description {
 font-size: 20px;
 color: #6b6f70;
 font-weight: 400;
 padding-top: 4px;
 padding-left: 50px;
 line-height: 1.3;
}

/* Customize scrollbar appearance */
.menu-items::-webkit-scrollbar {
 width: 10px;
}

.menu-items::-webkit-scrollbar-track {
 background: #f1f1f1;
 border-radius: 4px;
}

.menu-items::-webkit-scrollbar-thumb {
 background: #888;
 border-radius: 4px;
}

.menu-items::-webkit-scrollbar-thumb:hover {
 background: #555;
}

/* For the menu-content scrollbar */
.menu-content::-webkit-scrollbar {
 width: 4px;
}

.menu-content::-webkit-scrollbar-track {
 background: #f1f1f1;
 border-radius: 4px;
}

.menu-content::-webkit-scrollbar-thumb {
 background: #888;
 border-radius: 4px;
}

.menu-content::-webkit-scrollbar-thumb:hover {
 background: #555;
}

/* Responsive styling */
@media (max-width: 768px) {
 .menu-content {
  left: 20px;
  top: 20px;
  width: calc(100vw - 40px);
  max-width: 527px;
  height: 80vh;
  padding-bottom: 40px;
 }

 .sticky-menu {
  left: 15px;
  top: 15px;
 }

 .menu-toggle {
  width: 42px;
  height: 42px;
 }

 .menu-toggle img {
  width: 32px;
  height: 32px;
 }

 #menu-loyalty-link {
  font-size: 22px;
  padding-bottom: 10px;
 }

 .journey-item-number,
 .journey-item-title,
 .journey-item-description {
  font-size: 18px;
 }

 .journey-item-description {
  padding-left: 40px;
 }

 .menu-item-title {
  font-size: 18px;
  padding-bottom: 10px;
 }

 .journey-item-header {
  gap: 15px;
 }
}

@media (max-width: 760px) {
 .menu-content {
  left: 0;
  top: 50px;
  width: 100%;
  max-width: 100%;
  margin: 20px 25px;
  width: calc(100% - 60px);
 }

 #menu-loyalty-link {
  font-size: 20px;
  margin-bottom: 8px;
 }

 .journey-item-number,
 .journey-item-title {
  font-size: 16px;
 }

 .journey-item-description {
  font-size: 16px;
  padding-left: 35px;
 }

 .menu-close {
  top: 15px;
  right: 15px;
 }

 .menu-close img {
  width: 16px;
  height: 16px;
 }
}

@media (max-width: 350px) {
 .journey-item-number {
  min-width: 20px;
 }

 .journey-item-description {
  padding-left: 30px;
 }

 .menu-toggle {
  width: 36px;
  height: 36px;
 }

 .menu-toggle img {
  width: 28px;
  height: 28px;
 }
}

@media (max-height: 600px) {
 .menu-content {
  padding-top: 20px;
  height: calc(100vh - 30px);
 }

 .menu-close {
  top: 10px;
  right: 10px;
 }

 #menu-loyalty-link {
  margin-bottom: 5px;
  padding-bottom: 5px;
 }

 .menu-journey-item {
  padding: 5px 0;
 }

 .journey-item-description {
  padding-top: 2px;
 }
}
