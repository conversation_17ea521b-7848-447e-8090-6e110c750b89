const GOOGLE_SCRIPT_URL =
 "https://script.google.com/macros/s/AKfycbyGO5v2J_x5iuYM0eq2Eff5VAQOVl4pCyybk94gmlwM8AuRfEXszyAj4Maxu8nrswl4/exec";

document.addEventListener("DOMContentLoaded", function () {
 const form = document.getElementById("custom-form");
 const closeButton = document.querySelector(".close-button");

 const errorMessages = {
  name: document.getElementById("nameError"),
  surname: document.getElementById("surnameError"),
  email: document.getElementById("emailError"),
  privacyPolicy: document.getElementById("privacyError"),
 };

 if (closeButton) {
  closeButton.addEventListener("click", function () {
   window.parent.postMessage({ type: "closePopup" }, "*");
  });
 }

 function validateEmail(email) {
  const re =
   /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;

  // List of common personal email domains to reject
  const personalDomains = [
   "gmail.com",
   "hotmail.com",
   "outlook.com",
   "yahoo.com",
   "aol.com",
   "icloud.com",
   "mail.com",
   "protonmail.com",
   "zoho.com",
   "yandex.com",
   "gmx.com",
   "live.com",
   "msn.com",
   "me.com",
   "mac.com",
   "inbox.com",
   "fastmail.com",
   "proton.me",
  ];

  const emailLower = String(email).toLowerCase();
  const domain = emailLower.split("@")[1];

  if (!re.test(emailLower)) {
   return false;
  }

  // Check if the domain is in the list of personal domains
  if (personalDomains.includes(domain)) {
   return false;
  }

  return true;
 }

 function validateNameField(value) {
  const re = /^[A-Za-z\s-]{1,30}$/;
  return re.test(value);
 }

 function validateField(field) {
  const input = document.getElementById(field);
  const errorMsg = errorMessages[field];

  if (!input.value.trim()) {
   errorMsg.textContent = `${
    field.charAt(0).toUpperCase() + field.slice(1)
   } is required`;
   errorMsg.style.display = "block";
   input.classList.add("error-input");
   return false;
  } else if (
   (field === "name" || field === "surname") &&
   !validateNameField(input.value.trim())
  ) {
   errorMsg.textContent = "Invalid field";
   errorMsg.style.display = "block";
   input.classList.add("error-input");
   return false;
  } else if (field === "email" && !validateEmail(input.value.trim())) {
   errorMsg.textContent = "Please enter a valid company email address";
   errorMsg.style.display = "block";
   input.classList.add("error-input");
   return false;
  } else {
   errorMsg.style.display = "none";
   input.classList.remove("error-input");
   return true;
  }
 }

 function validatePrivacyPolicy() {
  const privacyCheckbox = document.getElementById("privacy_policy");
  const errorMsg = errorMessages.privacyPolicy;
  const label = document.getElementById("privacyLabel");

  if (!privacyCheckbox.checked) {
   errorMsg.textContent = "You must accept the Privacy Policy";
   errorMsg.style.display = "block";
   privacyCheckbox.classList.add("error-checkbox");
   label.classList.add("error-label");
   return false;
  } else {
   errorMsg.style.display = "none";
   privacyCheckbox.classList.remove("error-checkbox");
   label.classList.remove("error-label");
   return true;
  }
 }

 form.addEventListener("submit", function (e) {
  e.preventDefault();

  const isNameValid = validateField("name");
  const isSurnameValid = validateField("surname");
  const isEmailValid = validateField("email");
  const isPrivacyValid = validatePrivacyPolicy();

  if (isNameValid && isSurnameValid && isEmailValid && isPrivacyValid) {
   determineOrigin();

   const data = {
    name: document.getElementById("name").value,
    surname: document.getElementById("surname").value,
    email: document.getElementById("email").value,
    company: document.getElementById("company").value,
    role: document.getElementById("role").value,
    privacy_policy: document.getElementById("privacy_policy").checked
     ? "Yes"
     : "No",
    receive_more_info: document.getElementById("receive_more_info").checked
     ? "Yes"
     : "No",
    origin: document.getElementById("origin").value,
   };

   try {
    fetch(GOOGLE_SCRIPT_URL, {
     method: "POST",
     body: JSON.stringify(data),
     headers: {
      "Content-Type": "application/json",
     },
     mode: "no-cors",
    })
     .then((response) => {
      // Save form submission to localStorage to prevent showing popup again
      localStorage.setItem("customerEngagementFormSubmitted", "true");
      localStorage.setItem(
       "customerEngagementFormData",
       JSON.stringify({
        email: data.email,
        submittedAt: new Date().toISOString(),
       })
      );

      window.parent.postMessage(
       {
        type: "closePopup",
        action: "loadLoyalty",
        formData: data,
        formSubmitted: true,
        showStickyMenu: true,
       },
       "*"
      );

      form.reset();
     })
     .catch(() => {
      alert("There was an error submitting the form. Please try again later.");
     });
   } catch {
    alert(
     "There was an error processing your submission. Please try again later."
    );
   }
  }
 });

 document
  .getElementById("name")
  .addEventListener("blur", () => validateField("name"));
 document
  .getElementById("surname")
  .addEventListener("blur", () => validateField("surname"));
 document
  .getElementById("email")
  .addEventListener("blur", () => validateField("email"));
 document
  .getElementById("privacy_policy")
  .addEventListener("change", validatePrivacyPolicy);

 function determineOrigin() {
  // Get the origin from URL parameters
  const urlParams = new URLSearchParams(window.location.search);
  const originParam = urlParams.get("origin");

  if (originParam) {
   // Set the origin value in the hidden field
   document.getElementById("origin").value = originParam;
  }
 }

 determineOrigin();
});
