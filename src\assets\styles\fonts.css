/* Raleway Font Family */
@font-face {
 font-family: "Raleway";
 src: url("../../assets/fonts/Raleway-Regular.ttf") format("truetype");
 font-weight: 400;
 font-style: normal;
}

@font-face {
 font-family: "Raleway";
 src: url("../../assets/fonts/Raleway-Italic.ttf") format("truetype");
 font-weight: 400;
 font-style: italic;
}

@font-face {
 font-family: "Raleway";
 src: url("../../assets/fonts/Raleway-Light.ttf") format("truetype");
 font-weight: 300;
 font-style: normal;
}

@font-face {
 font-family: "Raleway";
 src: url("../../assets/fonts/Raleway-LightItalic.ttf") format("truetype");
 font-weight: 300;
 font-style: italic;
}

@font-face {
 font-family: "Raleway";
 src: url("../../assets/fonts/Raleway-Medium.ttf") format("truetype");
 font-weight: 500;
 font-style: normal;
}

@font-face {
 font-family: "Raleway";
 src: url("../../assets/fonts/Raleway-MediumItalic.ttf") format("truetype");
 font-weight: 500;
 font-style: italic;
}

@font-face {
 font-family: "Raleway";
 src: url("../../assets/fonts/Raleway-SemiBold.ttf") format("truetype");
 font-weight: 600;
 font-style: normal;
}

@font-face {
 font-family: "Raleway";
 src: url("../../assets/fonts/Raleway-SemiBoldItalic.ttf") format("truetype");
 font-weight: 600;
 font-style: italic;
}

@font-face {
 font-family: "Raleway";
 src: url("../../assets/fonts/Raleway-Bold.ttf") format("truetype");
 font-weight: 700;
 font-style: normal;
}

@font-face {
 font-family: "Raleway";
 src: url("../../assets/fonts/Raleway-BoldItalic.ttf") format("truetype");
 font-weight: 700;
 font-style: italic;
}

/* Playfair Display Font Family */
@font-face {
 font-family: "Playfair Display";
 src: url("../../assets/fonts/PlayfairDisplay-Regular.ttf") format("truetype");
 font-weight: 400;
 font-style: normal;
}

@font-face {
 font-family: "Playfair Display";
 src: url("../../assets/fonts/PlayfairDisplay-Italic.ttf") format("truetype");
 font-weight: 400;
 font-style: italic;
}

@font-face {
 font-family: "Playfair Display";
 src: url("../../assets/fonts/PlayfairDisplay-Medium.ttf") format("truetype");
 font-weight: 500;
 font-style: normal;
}

@font-face {
 font-family: "Playfair Display";
 src: url("../../assets/fonts/PlayfairDisplay-MediumItalic.ttf")
  format("truetype");
 font-weight: 500;
 font-style: italic;
}

@font-face {
 font-family: "Playfair Display";
 src: url("../../assets/fonts/PlayfairDisplay-SemiBold.ttf") format("truetype");
 font-weight: 600;
 font-style: normal;
}

@font-face {
 font-family: "Playfair Display";
 src: url("../../assets/fonts/PlayfairDisplay-SemiBoldItalic.ttf")
  format("truetype");
 font-weight: 600;
 font-style: italic;
}

@font-face {
 font-family: "Playfair Display";
 src: url("../../assets/fonts/PlayfairDisplay-Bold.ttf") format("truetype");
 font-weight: 700;
 font-style: normal;
}

@font-face {
 font-family: "Playfair Display";
 src: url("../../assets/fonts/PlayfairDisplay-BoldItalic.ttf")
  format("truetype");
 font-weight: 700;
 font-style: italic;
}

/* Base Typography Styles */
:root {
 --font-primary: "Raleway", -apple-system, BlinkMacSystemFont, "Segoe UI",
  Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
 --font-secondary: "Playfair Display", Georgia, "Times New Roman", serif;

 --font-weight-light: 300;
 --font-weight-regular: 400;
 --font-weight-medium: 500;
 --font-weight-semibold: 600;
 --font-weight-bold: 700;
}

/* Global Typography */
html {
 font-size: 16px;
 font-family: var(--font-primary);
 line-height: 1.5;
 -webkit-font-smoothing: antialiased;
 -moz-osx-font-smoothing: grayscale;
}

body {
 font-family: var(--font-primary);
 font-weight: var(--font-weight-regular);
 color: #333333;
}

/* Headings */
h1,
h2,
h3,
h4,
h5,
h6 {
 font-family: var(--font-secondary);
 font-weight: var(--font-weight-bold);
 line-height: 1.2;
 margin: 0 0 1rem;
}

p {
 font-size: 20px;
}

strong {
 font-weight: 700;
}
