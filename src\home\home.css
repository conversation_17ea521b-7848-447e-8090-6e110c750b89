:root {
 --body-font: "Ralew<PERSON>", Helvetica;
 --title-font: "Playfair Display", Helvetica;
 --primary-color: #292b33;
 --accent-color: #f18f86;
 --gray-color: #6b6f70;
}

* {
 margin: 0;
 padding: 0;
 box-sizing: border-box;
}

body {
 font-family: var(--body-font);
 background-color: white;
 color: var(--primary-color);
 line-height: 1.6;
}

.container {
 max-width: 1290px;
 margin: 0 auto;
 padding: 0 20px;
}

/* Hero Section */
.hero {
 padding: 128px 0;
 text-align: center;
}

.hero-title {
 font-size: 50px;
 line-height: 70px;
 font-weight: normal;
}

.bold {
 font-family: var(--title-font);
 font-weight: 700;
}

/* Introduction Section */
.intro {
 padding: 80px 0;
}

.body-text {
 font-size: 20px;
 line-height: 26px;
 margin-bottom: 50px;
}

.question-block {
 text-align: center;
 margin: 50px 0;
}

.question-intro {
 font-weight: bold;
 font-size: 20px;
 margin-bottom: 8px;
}

.main-question {
 font-size: 35px;
 font-weight: 600;
}

.italic {
 font-family: var(--title-font);
 font-style: italic;
 font-weight: 700;
}

/* Statistics Section */
.stats-grid {
 display: flex;
 flex-wrap: wrap;
 gap: 32px;
 justify-content: space-between;
}

.stat-card {
 width: 545px;
}

.stat-number {
 font-family: var(--title-font);
 font-size: 70px;
 font-weight: 700;
 color: var(--accent-color);
 line-height: 1.14;
 margin-bottom: 25px;
}

.progress-bar {
 width: 100%;
 height: 15px;
 background-color: var(--gray-color);
 border-radius: 7.5px;
 margin-bottom: 25px;
}

.progress {
 height: 100%;
 background-color: var(--accent-color);
 border-radius: 7.5px;
}

.stat-description {
 font-size: 26px;
 line-height: 32px;
}

/* Benefits Section */
.benefits {
 padding: 80px 0;
}

.benefits-list {
 width: 100%;
}

.benefit-item {
 display: flex;
 justify-content: space-between;
 align-items: center;
 padding: 20px 0;
 border-bottom: 2px solid #e5e7eb;
}

.benefit-title {
 font-family: var(--title-font);
 font-size: 36px;
 font-weight: normal;
}

.benefit-toggle {
 display: flex;
 align-items: center;
 justify-content: center;
 width: 24px;
 height: 24px;
}

.benefit-toggle img {
 width: 24px;
 height: 24px;
 filter: brightness(0) saturate(100%); /* Makes the SVG black */
}

/* Advocacy Section */
.advocacy {
 padding: 64px 0;
}

.advocacy-grid {
 display: flex;
 flex-wrap: wrap;
}

.advocacy-image {
 width: 50%;
}

.advocacy-image img {
 width: 100%;
 height: auto;
 max-height: 885px;
 object-fit: cover;
}

.advocacy-content {
 width: 50%;
 padding: 32px;
 display: flex;
 flex-direction: column;
 justify-content: space-between;
}

.advocacy-title {
 font-family: var(--title-font);
 font-size: 50px;
 font-weight: 700;
 line-height: 1.4;
}

/* Mobile Menu Button */
.mobile-menu-btn {
 position: fixed;
 top: 57px;
 left: 57px;
 width: 36px;
 height: 36px;
 background-color: var(--gray-color);
 border: none;
 border-radius: 18px;
 cursor: pointer;
 display: flex;
 align-items: center;
 justify-content: center;
}

.menu-icon {
 width: 16px;
 height: 24px;
 display: flex;
 flex-direction: column;
 justify-content: space-between;
}

.dot-line {
 display: flex;
 align-items: center;
}

.dot {
 width: 3px;
 height: 3px;
 background-color: white;
 border-radius: 1.29px;
}

.line {
 width: 16px;
 height: 2px;
 margin-left: 4px;
 background-color: white;
}

/* Final Section */
.final {
 margin-top: 64px;
 background-color: white;
 border-radius: 98px 98px 0 0;
 box-shadow: 0px -5px 20px rgba(0, 0, 0, 0.15);
 padding: 80px 0 128px;
 text-align: center;
}

.final-title {
 font-size: 50px;
 line-height: 70px;
 font-weight: normal;
}

/* Responsive Design */
@media (max-width: 1024px) {
 .advocacy-image,
 .advocacy-content {
  width: 100%;
 }

 .stats-grid {
  justify-content: center;
 }

 .stat-card {
  width: 100%;
  max-width: 545px;
 }
}

@media (max-width: 768px) {
 .hero-title,
 .advocacy-title,
 .final-title {
  font-size: 36px;
  line-height: 1.4;
 }

 .main-question {
  font-size: 28px;
 }

 .body-text {
  font-size: 18px;
 }

 .benefit-title {
  font-size: 28px;
 }
}
