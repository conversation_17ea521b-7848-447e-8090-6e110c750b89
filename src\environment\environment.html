<!DOCTYPE html>
<html lang="en">
 <head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Environment</title>
  <link
   href="https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;500;600;700&family=Raleway:wght@400;600;700&display=swap"
   rel="stylesheet"
  />
  <link rel="stylesheet" href="environment.css" />
 </head>
 <body>
  <!-- Initial container with the intro content -->
  <div class="environment-container">
   <!-- Intro content visible on initial load -->
   <div class="main-text">
    <h1>
     Turning awareness into advocacy is a nuanced and delicate process shaped by
     three key factors:
     <b>environments, experiences, and emotions.</b>
    </h1>
    <p>
     Understanding how these elements influence customer interactions is
     essential for building deep, lasting engagement.
    </p>
   </div>
  </div>

  <div class="scroll-container">
   <section class="scroll-section" id="intro">
    <div class="content-wrapper intro-wrapper">
     <div class="main-text">
      <h1>
       Turning awareness into advocacy is a nuanced and delicate process shaped
       by three key factors:
       <b>environments, experiences, and emotions.</b>
      </h1>
      <p>
       Understanding how these elements influence customer interactions is
       essential for building deep, lasting engagement.
      </p>
     </div>
    </div>
   </section>

   <section class="scroll-section" id="environments">
    <div class="content-wrapper">
     <h1 class="title">The Environments:</h1>
     <div class="questions-container">
      <h2 class="question">
       Where and how do customers interact with your brand?
      </h2>
      <h2 class="question">
       Are your physical and digital spaces accesible, inviting and intuitive?
      </h2>
      <p class="description">
       A well-designed environment removes friction and fosters a seamless
       experience.
      </p>
     </div>
    </div>
   </section>

   <section class="scroll-section" id="experiences">
    <div class="content-wrapper">
     <h1 class="title">The Experiences:</h1>
     <div class="questions-container">
      <h2 class="question">
       What experiences are you providing within those environments?
      </h2>
      <h2 class="question">
       Do they align with your brand's values and messaging, or is there
       dissonance?
      </h2>
      <h2 class="question">
       Are these experiences memorable in a positive way?
      </h2>
      <p class="description">
       Every touchpoint should reinforce trust and engagement.
      </p>
     </div>
    </div>
   </section>

   <section class="scroll-section" id="emotions">
    <div class="content-wrapper">
     <h1 class="title">The Emotions:</h1>
     <div class="questions-container">
      <h2 class="question">
       What emotions or feelings do these environments and experiences evoke?
      </h2>
      <h2 class="question">
       Are they building excitement, trust, and satisfaction, or causing
       frustration and disengagement?
      </h2>
      <p class="description">
       Strong emotional connections turn customers into loyal advocates.
      </p>
     </div>
    </div>
   </section>

   <section class="scroll-section" id="final-scroll">
    <div class="content-wrapper">
     <div class="final-content">
      <p class="final-text">
       Brands that start with the
       <strong>desired emotional outcome</strong> in mind can design
       interactions that truly resonate, and that will nudge customers towards
       brand advocacy. But at the heart of this process is the customer. Every
       decision should revolve around their needs, preferences, and
       expectations, as they are the foundation of what is trying to be built. 
      </p>
      <p class="final-text">
       Customer Engagement strategies, such as loyalty programmes, must go
       beyond the purely transactional to sustain customer interest in the long
       term. When brands use data-driven personalisation to recognise and reward
       customers meaningfully, they reinforce trust and strengthen the bond.
       Customers should feel valued, understood, and confident that their data
       is being used to enhance their experience, not just to drive sales. 
      </p>
     </div>
    </div>
   </section>
  </div>
  <script src="environment.js"></script>
 </body>
</html>
