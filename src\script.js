// Journey steps data
const journeySteps = [
 {
  number: "02",
  title: "Consideration:",
  description: "Convince, Intrigue and Excite",
 },
 {
  number: "03",
  title: "Enrolment:",
  description: "Turning Interest into Commitment",
 },
 {
  number: "04",
  title: "Participation:",
  description: "Encouraging Active Engagement",
 },
 {
  number: "05",
  title: "Retention:",
  description: "Keeping the Spark Alive",
 },
 {
  number: "06",
  title: "Advocacy:",
  description: "The Difference between a Loyal Customer and a Brand Ambassador",
 },
 {
  number: "07",
  title: "Advocacy:",
  description: "The Difference between a Loyal Customer and a Brand Ambassador",
 },
 {
  number: "08",
  title: "Advocacy:",
  description: "The Difference between a Loyal Customer and a Brand Ambassador",
 },
];

// Brand benefits data for loyalty section
const loyaltyBrandBenefits = [
 {
  title: "Competitive edge",
  content:
   "With endless choices at their fingertips, today's consumers are more selective than ever. Building a base of loyal customers can be a key differentiator, as brands that cultivate strong customer loyalty can really stand out from the competition. According to Accenture, 75% of consumers are more likely to buy from a brand that offers a loyalty programme. Building lasting relationships through these programmes fosters advocacy and long-term value for brands.",
 },
 {
  title: "Cost effective",
  content:
   "Acquiring new customers is expensive, but retaining loyal ones drives sustainable profitability. Loyal customers require less marketing spend, convert faster, and are more likely to make repeat purchases, reducing overall acquisition costs. Rather than constantly chasing new buyers, brands that invest in loyalty see stronger long-term returns.",
 },
 {
  title: "Increased lifetime value and purchase frequency",
  content:
   "Due to their connection with a brand, loyal customers buy more, buy more often, and are less price sensitive. Studies show they spend 67% more than new customers and tend to choose higher-margin products because they trust the brand's quality and value. This not only increases revenue but also strengthens brand resilience, especially during economic uncertainty.",
 },
 {
  title: "Brand consistency",
  content:
   "Beyond transactions, loyalty is built through trust, engagement, and consistent brand experiences. Customers who feel connected to a brand, through shared values, personalised interactions, and exceptional service, are more likely to remain devoted, even when competitors try to lure them away.",
 },
 {
  title: "Stronger brand advocacy",
  content:
   "Loyal customers don't just stick around, they become vocal supporters. They refer friends, share positive experiences, and amplify a brand's message through word-of-mouth and social media. Their advocacy is one of the most powerful (and cost-effective) forms of marketing, increasing credibility and attracting new customers naturally.",
 },
];

// Force scroll to top before anything else
if (history.scrollRestoration) {
 history.scrollRestoration = "manual";
}

// Handle page refresh
window.onbeforeunload = function () {
 window.scrollTo(0, 0);
};

// Initialize navigation state
window.navigableLinks = false;

// Wait for DOM to be fully loaded
document.addEventListener("DOMContentLoaded", () => {
 // Force scroll to top immediately
 window.scrollTo({
  top: 0,
  left: 0,
  behavior: "instant",
 });

 // Reset popup state on page load
 popupShown = false;

 // Clear URL hash if any
 if (window.location.hash) {
  window.location.hash = "";
 }

 // Don't show popup immediately - it will be triggered when user reaches end of content section

 // Populate journey steps
 const journeyStepsContainer = document.querySelector(".journey-steps");
 if (journeyStepsContainer) {
  journeyStepsContainer.innerHTML = ""; // Clear existing content
  journeySteps.forEach((step) => {
   const stepElement = document.createElement("div");
   stepElement.className = "step";

   stepElement.innerHTML = `
                <div class="step-number">${step.number}.</div>
                <div class="step-title">${step.title}</div>
                <div class="step-description">${step.description}</div>
            `;

   journeyStepsContainer.appendChild(stepElement);

   // Add click event to each journey step
   stepElement.addEventListener("click", (e) => {
    // Only process clicks if navigation is enabled (after popup is closed)
    if (window.navigableLinks) {
     // Load engagement content
     loadEngagementContent();

     // Create a message listener to wait for the engagement iframe to load
     const handleMessage = (event) => {
      // Only process messages from engagement.html
      if (event.data && event.data.type === "engagementLoaded") {
       // Remove this listener once we've processed the message
       window.removeEventListener("message", handleMessage);

       // Send a message to the iframe to scroll to the selected section
       const iframe = document.querySelector("#engagement-content iframe");
       if (iframe && iframe.contentWindow) {
        iframe.contentWindow.postMessage(
         {
          type: "scrollToSection",
          sectionId: step.number,
         },
         "*"
        );
       }
      }
     };

     // Add the message listener
     window.addEventListener("message", handleMessage);
    } else {
     // If links aren't navigable yet (popup not closed), load popup
     loadPopup();
    }
   });
  });
 }

 // Remove any existing scroll listeners
 window.removeEventListener("scroll", handleScroll);
 // Add fresh scroll listener
 window.addEventListener("scroll", handleScroll);
 // Initial check in case conclusion is already in view
 setTimeout(() => {
  // Double-check scroll position
  if (window.pageYOffset !== 0) {
   window.scrollTo({
    top: 0,
    left: 0,
    behavior: "instant",
   });
  }
  handleScroll();
 }, 100);

 // Add click event to loyalty arrow icon and bottom title
 const arrowIcon = document.querySelector(".loyalty-arrow-icon");
 const bottomTitle = document.querySelector(".loyalty-bottom-title");
 if (arrowIcon) {
  arrowIcon.addEventListener("click", (e) => {
   e.preventDefault();
   showEnvironmentContent();
  });
 }
 if (bottomTitle) {
  bottomTitle.style.cursor = "pointer";
  bottomTitle.addEventListener("click", (e) => {
   e.preventDefault();
   showEnvironmentContent();
  });
 }

 // Set up content navigation links (initially disabled, will be enabled after popup is closed)
 setupContentNavigationLinks();

 // Sticky Menu Functionality
 initializeStickyMenu();

 // Show sticky menu immediately on page load
 const stickyMenu = document.querySelector(".sticky-menu");
 if (stickyMenu) {
  stickyMenu.classList.add("show");
 }

 // Enable navigation links immediately
 if (window.enableContentNavigation) {
  window.enableContentNavigation();
 }

 // Menu functionality
 const menuToggle = document.querySelector(".menu-toggle");
 const menuContent = document.querySelector(".menu-content");
 const menuClose = document.querySelector(".menu-close");
 const menuItems = document.querySelector(".menu-items");
 let lastTouchY; // Add variable for touch handling

 menuContent.addEventListener(
  "touchstart",
  (e) => {
   lastTouchY = e.touches[0].clientY;
  },
  { passive: true }
 );

 function openMenu() {
  menuContent.classList.add("open");
  document.body.classList.add("menu-open");

  // Add wheel event listener directly to menu items container with higher z-index
  const menuItems = document.querySelector(".menu-items");
  if (menuItems) {
   menuItems.style.zIndex = "10002"; // Ensure menu items are above other content
   menuItems.addEventListener("wheel", handleMenuScroll, { passive: false });
   menuItems.addEventListener("touchmove", handleMenuScroll, {
    passive: false,
   });
  }
 }

 function closeMenu() {
  menuContent.classList.remove("open");
  document.body.classList.remove("menu-open");
  document.body.style.overflow = ""; // Restore body scrolling

  // Remove wheel event listener from menu items container
  const menuItems = document.querySelector(".menu-items");
  if (menuItems) {
   menuItems.removeEventListener("wheel", handleMenuScroll);
   menuItems.removeEventListener("touchmove", handleMenuScroll);
  }
 }

 function handleMenuScroll(event) {
  // Always prevent default scroll behavior
  event.preventDefault();
  event.stopPropagation();

  // Get the menu items container (this is the element being scrolled)
  const menuItems = event.currentTarget;

  if (event.type === "wheel") {
   // Calculate new scroll position
   const delta = event.deltaY;
   const scrollTop = menuItems.scrollTop;
   const scrollHeight = menuItems.scrollHeight;
   const clientHeight = menuItems.clientHeight;

   // Check if we're at the top or bottom of scroll
   if (
    (scrollTop <= 0 && delta < 0) ||
    (scrollTop + clientHeight >= scrollHeight && delta > 0)
   ) {
    return; // Prevent overscroll
   }

   menuItems.scrollTop += delta;

   // Stop event from propagating to environment section
   event.stopPropagation();
  } else if (event.type === "touchmove") {
   // Handle touch scrolling if needed
   const touch = event.touches[0];
   const deltaY = lastTouchY - touch.clientY;
   lastTouchY = touch.clientY;
   menuItems.scrollTop += deltaY;

   // Stop event from propagating to environment section
   event.stopPropagation();
  }
 }

 menuToggle.addEventListener("click", openMenu);
 menuClose.addEventListener("click", closeMenu);

 // Close menu when clicking outside
 document.addEventListener("click", (event) => {
  if (
   !menuContent.contains(event.target) &&
   !menuToggle.contains(event.target)
  ) {
   closeMenu();
  }
 });

 // Prevent default scroll behavior when pressing arrow keys and menu is open
 document.addEventListener("keydown", (event) => {
  if (
   document.body.classList.contains("menu-open") &&
   (event.key === "ArrowUp" || event.key === "ArrowDown")
  ) {
   event.preventDefault();
  }
 });

 // Content restriction and popup functionality
 const contentSection = document.querySelector(".content");

 // Function to manage visibility of restricted sections
 function updateSectionsVisibility() {
  const formSubmitted = hasUserSubmittedForm();

  if (formSubmitted) {
   // If form was submitted, show all content and remove access message
   const accessMessage = document.getElementById("access-message");
   if (accessMessage) {
    accessMessage.remove();
   }

   // Show the sticky menu
   const stickyMenu = document.querySelector(".sticky-menu");
   if (stickyMenu) {
    stickyMenu.classList.add("show");
   }
  } else {
   // Remove sections after content if form not submitted
   const sectionsToRemove = document.querySelectorAll(
    "section:not(.introduction-section):not(.content)"
   );

   // Remove each section from the DOM
   sectionsToRemove.forEach((section) => {
    section.remove();
   });

   // Create access message with unlock button
   createAccessMessage();
  }
 }

 // Create access message with unlock button
 function createAccessMessage() {
  if (hasUserSubmittedForm() || document.getElementById("access-message")) {
   return; // Don't create if form already submitted or message already exists
  }

  const accessMessage = document.createElement("div");
  accessMessage.id = "access-message";
  accessMessage.innerHTML = `
    <div class="message-content">
      <h3>Complete the form to unlock the full content</h3>
      <button id="unlock-button"><h3>Unlock Now</h3></button>
    </div>
  `;
  document.body.appendChild(accessMessage);

  // Add direct click handler to the button
  const unlockButton = document.getElementById("unlock-button");
  if (unlockButton) {
   unlockButton.onclick = function (e) {
    e.preventDefault();
    loadPopup();
    return false;
   };
  }
 }

 // Function to check if user has reached the end of content section
 function checkContentScroll() {
  if (!contentSection || popupShown || hasUserSubmittedForm()) return;

  const rect = contentSection.getBoundingClientRect();
  const isAtBottom = rect.bottom <= window.innerHeight + 50; // Add 50px buffer

  // If user scrolls to the bottom of content section
  if (isAtBottom) {
   // Show popup to unlock content
   loadPopup();
   popupShown = true;
  }
 }

 // Apply initial visibility state
 updateSectionsVisibility();

 // Add scroll listener to check when user reaches end of content
 window.addEventListener("scroll", checkContentScroll);

 // Show loyalty content by default and initialize its functionality
 showLoyaltyContent(false); // Don't hide main content initially
});

// Handle complete page load
window.addEventListener("load", () => {
 // Force scroll to top again after all resources are loaded
 window.scrollTo({
  top: 0,
  left: 0,
  behavior: "instant",
 });

 // Reset any scroll position that might have been restored
 setTimeout(() => {
  window.scrollTo({
   top: 0,
   left: 0,
   behavior: "instant",
  });
 }, 50);
});

// Function to check if an element is in viewport
function isElementInViewport(el) {
 const rect = el.getBoundingClientRect();
 return (
  rect.top >= 0 &&
  rect.left >= 0 &&
  rect.bottom <=
   (window.innerHeight || document.documentElement.clientHeight) &&
  rect.right <= (window.innerWidth || document.documentElement.clientWidth)
 );
}

// Function to load and show popup
function loadPopup(navigateTo = null) {
 // Remove any existing popups first
 const existingPopup = document.querySelector(
  'iframe[src*="popup/popup.html"]'
 );
 const existingOverlay = document.querySelector(".popup-overlay");
 if (existingPopup) {
  existingPopup.remove();
 }
 if (existingOverlay) {
  existingOverlay.remove();
 }

 // Create overlay
 const overlay = document.createElement("div");
 overlay.className = "popup-overlay";
 overlay.style.position = "fixed";
 overlay.style.top = "0";
 overlay.style.left = "0";
 overlay.style.width = "100%";
 overlay.style.height = "100%";
 overlay.style.backgroundColor = "rgba(0, 0, 0, 0.5)";
 overlay.style.zIndex = "999";

 // Create popup with optional navigation parameter
 let popupSrc = "popup/popup.html";
 if (navigateTo) {
  popupSrc += `?navigateTo=${navigateTo}`;
 }

 const popup = document.createElement("iframe");
 popup.src = popupSrc;
 popup.style.position = "fixed";
 popup.style.left = "50%";
 popup.style.top = "50%";
 popup.style.transform = "translate(-50%, -50%)";
 popup.style.width = "95%";
 popup.style.maxWidth = "1051px";
 popup.style.height = "min(815px, 90vh)";
 popup.style.border = "none";
 popup.style.borderRadius = "12px";
 popup.style.zIndex = "1000";
 popup.style.backgroundColor = "white";
 popup.style.boxShadow = "0 5px 30px rgba(0, 0, 0, 0.15)";
 popup.style.overflowX = "hidden";

 // Add base styles to prevent overflow in the iframe content
 popup.onload = function () {
  try {
   const iframeDoc = popup.contentDocument || popup.contentWindow.document;
   const style = iframeDoc.createElement("style");
   style.textContent = `
                body {
                    margin: 0;
                    padding: 24px;
                    box-sizing: border-box;
                    max-width: 100%;
                    overflow-x: hidden;
                }
                * {
                    box-sizing: border-box;
                    max-width: 100%;
                }
            `;
   iframeDoc.head.appendChild(style);
  } catch (e) {
   console.log(
    "Note: Could not inject styles into iframe due to same-origin policy"
   );
  }
 };

 // Handle messages from iframe for close button
 const messageHandler = function (event) {
  if (event.data && event.data.type === "closePopup") {
   // Store current scroll position
   const scrollPosition = window.scrollY;

   // Remove popup elements
   document.body.removeChild(overlay);
   document.body.removeChild(popup);
   window.removeEventListener("message", messageHandler);

   // Call the dedicated function to handle popup closing
   // Pass any navigation target received from the popup
   handlePopupClose(scrollPosition, event.data.navigateTo);
  }
 };
 window.addEventListener("message", messageHandler);

 document.body.appendChild(overlay);
 document.body.appendChild(popup);
}

// Global popup state
let popupShown = false;

// Global state for advocate content
let advocateContentLoaded = false;

// Function to check if user has already submitted the form
function hasUserSubmittedForm() {
 try {
  const formSubmitted = localStorage.getItem("customerEngagementFormSubmitted");
  return formSubmitted === "true";
 } catch (error) {
  // If localStorage is not available, don't show popup to be safe
  console.log("localStorage not available:", error);
  return false;
 }
}

// Function to clear form submission state (for testing purposes)
// You can call this in browser console: clearFormSubmissionState()
window.clearFormSubmissionState = function () {
 try {
  localStorage.removeItem("customerEngagementFormSubmitted");
  localStorage.removeItem("customerEngagementFormData");
  console.log(
   "Form submission state cleared. Refresh the page to see the popup again."
  );
 } catch (error) {
  console.log("Error clearing localStorage:", error);
 }
};

// Check if conclusion section is in view and show popup
function handleScroll() {
 // Popup is now shown on page load, so this function is kept for compatibility
 // but no longer triggers the popup based on scroll position
}

// Function to show loyalty content
function showLoyaltyContent(hideMainContent = true) {
 // Hide intro and main content if requested
 if (hideMainContent) {
  document.querySelector(".introduction-section").style.display = "none";
  document.querySelector(".main-content").style.display = "none";
 }

 // Show loyalty content
 const loyaltyContent = document.getElementById("loyalty-content");
 if (loyaltyContent) {
  // Show content
  loyaltyContent.style.display = "block";
  loyaltyContent.style.opacity = "1";

  // Initialize loyalty functionality
  initializeLoyaltyBenefits();
  initializeLoyaltyPopup();
  initializeLoyaltyAnimations();

  // Apply animations using the textanimate.js functions
  setTimeout(() => {
   if (window.initSlideInAnimations) {
    window.initSlideInAnimations();
   }
  }, 100);
 }
}

// Initialize benefits list for loyalty section
function initializeLoyaltyBenefits() {
 const benefitsList = document.getElementById("loyaltyBenefitsList");
 if (!benefitsList) return;

 loyaltyBrandBenefits.forEach((benefit, index) => {
  const benefitItem = document.createElement("div");
  benefitItem.className = "loyalty-benefit-item";
  benefitItem.dataset.index = index;

  // Item container for animation
  const itemContainer = document.createElement("div");
  itemContainer.className = "loyalty-benefit-item-container";

  // Header with title and toggle button
  const header = document.createElement("div");
  header.className = "loyalty-benefit-header";

  header.innerHTML = `
            <h3 class="loyalty-benefit-title">${benefit.title}</h3>
            <span class="loyalty-benefit-toggle">
                <img src="assets/images/arrow-icon.svg" alt="Toggle" />
            </span>
        `;

  // Content section (initially hidden)
  const content = document.createElement("div");
  content.className = "loyalty-benefit-content";
  content.textContent = benefit.content;

  // Content wrapper for smooth height animation
  const contentWrapper = document.createElement("div");
  contentWrapper.className = "loyalty-benefit-content-wrapper";
  contentWrapper.appendChild(content);

  // Divider line
  const divider = document.createElement("div");
  divider.className = "loyalty-benefit-divider";

  // Add elements to the item container
  itemContainer.appendChild(header);
  itemContainer.appendChild(contentWrapper);
  itemContainer.appendChild(divider);

  // Add the container to the item
  benefitItem.appendChild(itemContainer);

  // Add click event to toggle content
  header.addEventListener("click", () => {
   const isOpen = contentWrapper.classList.contains("open");
   const toggle = header.querySelector(".loyalty-benefit-toggle");

   // Close all other items first
   const allWrappers = benefitsList.querySelectorAll(
    ".loyalty-benefit-content-wrapper"
   );
   const allToggles = benefitsList.querySelectorAll(".loyalty-benefit-toggle");

   // Close all other items with smooth animation
   allWrappers.forEach((wrapper) => {
    if (wrapper !== contentWrapper && wrapper.classList.contains("open")) {
     closeAccordionItem(wrapper);
    }
   });

   // Reset all other toggles
   allToggles.forEach((toggleEl) => {
    if (toggleEl !== toggle) {
     gsap.to(toggleEl, {
      rotation: 0,
      duration: 0.3,
      ease: "power2.out",
     });
    }
   });

   // Toggle the clicked item
   if (isOpen) {
    closeAccordionItem(contentWrapper);
    gsap.to(toggle, {
     rotation: 0,
     duration: 0.3,
     ease: "power2.out",
    });
   } else {
    openAccordionItem(contentWrapper);
    gsap.to(toggle, {
     rotation: 180,
     duration: 0.3,
     ease: "power2.out",
    });

    // Smooth scroll if needed after animation completes
    setTimeout(() => {
     const rect = benefitItem.getBoundingClientRect();
     const isFullyVisible = rect.top >= 0 && rect.bottom <= window.innerHeight;

     if (!isFullyVisible) {
      gsap.to(window, {
       scrollTo: {
        y: window.scrollY + rect.top - 100,
        autoKill: false,
       },
       duration: 0.6,
       ease: "power2.out",
      });
     }
    }, 300);
   }
  });

  benefitsList.appendChild(benefitItem);
 });

 // Initialize Intersection Observer for loyalty scroll animations
 initializeLoyaltyScrollAnimation();
}

// Function to handle loyalty scroll animations
function initializeLoyaltyScrollAnimation() {
 const benefitItems = document.querySelectorAll(".loyalty-benefit-item");

 // First pass to add the animated class with staggered timing
 const observerOptions = {
  root: null,
  rootMargin: "0px",
  threshold: 0.1,
 };

 const observer = new IntersectionObserver((entries) => {
  entries.forEach((entry) => {
   if (entry.isIntersecting) {
    const target = entry.target;
    const index = parseInt(target.dataset.index);

    // Add animation class with delay based on index
    setTimeout(() => {
     target.classList.add("animated");
    }, index * 200);

    // Unobserve after animation is applied
    observer.unobserve(target);
   }
  });
 }, observerOptions);

 // Observe all benefit items for initial animation
 benefitItems.forEach((item) => {
  observer.observe(item);
 });

 // Note: Removed automatic scroll-based accordion opening
 // Accordion items will only open when clicked by the user
}

// Helper function to open accordion item with smooth height animation
function openAccordionItem(contentWrapper) {
 const content = contentWrapper.querySelector(".loyalty-benefit-content");

 // Add open class
 contentWrapper.classList.add("open");

 // Get the natural height
 const naturalHeight = content.scrollHeight;

 // Animate from 0 to natural height
 gsap.fromTo(
  contentWrapper,
  {
   height: 0,
   opacity: 0,
  },
  {
   height: naturalHeight + "px",
   opacity: 1,
   duration: 0.4,
   ease: "power2.out",
   onComplete: () => {
    // Set height to auto for responsive behavior
    contentWrapper.style.height = "auto";
   },
  }
 );
}

// Helper function to close accordion item with smooth height animation
function closeAccordionItem(contentWrapper) {
 // Get current height
 const currentHeight = contentWrapper.offsetHeight;

 // Set explicit height before animating
 contentWrapper.style.height = currentHeight + "px";

 // Animate to 0 height
 gsap.to(contentWrapper, {
  height: 0,
  opacity: 0,
  duration: 0.3,
  ease: "power2.out",
  onComplete: () => {
   // Remove open class after animation
   contentWrapper.classList.remove("open");
  },
 });
}

// Initialize popup for loyalty section
function initializeLoyaltyPopup() {
 const statBadge = document.getElementById("loyaltyQuitStatBadge");
 const popup = document.getElementById("loyaltyQuitPopup");
 const overlay = document.getElementById("loyaltyPopupOverlay");
 const closeBtn = popup ? popup.querySelector(".loyalty-popup-close") : null;

 if (!statBadge || !popup || !overlay || !closeBtn) return;

 function openLoyaltyPopup() {
  // Store the current scroll position
  const scrollPosition = window.scrollY;

  // Display popup and overlay
  popup.style.display = "block";
  overlay.style.display = "block";

  // Simple fade-in animation
  gsap.fromTo(
   popup,
   { opacity: 0 },
   { opacity: 1, duration: 0.3, ease: "power1.out" }
  );

  gsap.to(overlay, { opacity: 1, duration: 0.3 });

  // Set a data attribute to store the scroll position
  document.body.setAttribute("data-scroll-position", scrollPosition.toString());

  // Prevent background scrolling
  document.body.style.overflow = "hidden";
 }

 function closeLoyaltyPopup() {
  // Get the stored scroll position
  const scrollPosition = parseInt(
   document.body.getAttribute("data-scroll-position") || "0"
  );

  // Get the loyalty section element
  const loyaltySection = document.querySelector(".loyalty-content-section");
  const loyaltyRect = loyaltySection
   ? loyaltySection.getBoundingClientRect()
   : null;

  // Simple fade-out animation
  gsap.to(popup, {
   opacity: 0,
   duration: 0.3,
   ease: "power1.in",
   onComplete: function () {
    popup.style.display = "none";

    // Restore scroll position
    window.scrollTo({
     top: scrollPosition,
     behavior: "auto",
    });

    // After restoring scroll, ensure loyalty section is in view
    if (
     loyaltyRect &&
     (loyaltyRect.top < 0 || loyaltyRect.bottom > window.innerHeight)
    ) {
     loyaltySection.scrollIntoView({ behavior: "smooth", block: "center" });
    }

    // Re-enable scrolling
    document.body.style.overflow = "";
   },
  });

  gsap.to(overlay, {
   opacity: 0,
   duration: 0.3,
   onComplete: function () {
    overlay.style.display = "none";
   },
  });
 }

 // Event listeners
 statBadge.addEventListener("click", openLoyaltyPopup);
 closeBtn.addEventListener("click", closeLoyaltyPopup);
 overlay.addEventListener("click", closeLoyaltyPopup);
}

// New function to initialize all loyalty animations
function initializeLoyaltyAnimations() {
 gsap.registerPlugin(ScrollTrigger);

 // Split and animate the awareness to advocacy title
 const awarenessTitle = document.querySelector(".loyalty-content-text h1");
 if (awarenessTitle) {
  const titleParts = awarenessTitle.textContent.split(":");
  awarenessTitle.innerHTML = `
            <div class="awareness-title">${titleParts[0]}:</div>
            <div class="awareness-subtitle">${titleParts[1]}</div>
        `;

  // Create scroll trigger for title animation
  ScrollTrigger.create({
   trigger: awarenessTitle,
   start: "top 85%",
   onEnter: () => {
    document.querySelector(".awareness-title").classList.add("animate");
    document.querySelector(".awareness-subtitle").classList.add("animate");
   },
   onLeaveBack: () => {
    document.querySelector(".awareness-title").classList.remove("animate");
    document.querySelector(".awareness-subtitle").classList.remove("animate");
   },
  });
 }

 // Animate the badge elements (75% would quit!)
 const badgeElements = document.querySelectorAll(
  ".loyalty-percentage, .loyalty-text"
 );
 if (badgeElements.length) {
  const badgeTimeline = gsap.timeline({
   scrollTrigger: {
    trigger: ".loyalty-statistic-badge",
    start: "top 85%",
    toggleActions: "play none none reverse",
   },
  });

  badgeTimeline
   .fromTo(
    badgeElements[0], // 75%
    { opacity: 0, y: 30 },
    { opacity: 1, y: 0, duration: 0.6, ease: "power2.out" }
   )
   .fromTo(
    badgeElements[1], // would quit!
    { opacity: 0, y: 30 },
    { opacity: 1, y: 0, duration: 0.6, ease: "power2.out" },
    "-=0.3"
   );
 }

 // Animate the bottom section (From Awareness to Advocacy)
 const bottomSectionElements = document.querySelectorAll(
  ".loyalty-bottom-title, .loyalty-bottom-subtitle, .loyalty-arrow-icon"
 );
 if (bottomSectionElements.length) {
  const bottomTimeline = gsap.timeline({
   scrollTrigger: {
    trigger: ".loyalty-bottom-section",
    start: "top 85%",
    toggleActions: "play none none reverse",
   },
  });

  bottomTimeline
   .fromTo(
    bottomSectionElements[0], // title
    { opacity: 0, y: 30 },
    { opacity: 1, y: 0, duration: 0.6, ease: "power2.out" }
   )
   .fromTo(
    bottomSectionElements[1], // subtitle
    { opacity: 0, y: 30 },
    { opacity: 1, y: 0, duration: 0.6, ease: "power2.out" },
    "-=0.3"
   )
   .fromTo(
    bottomSectionElements[2], // arrow icon
    { opacity: 0, y: 30 },
    { opacity: 1, y: 0, duration: 0.6, ease: "power2.out" },
    "-=0.3"
   );
 }

 // Animate hero section
 gsap.fromTo(
  ".loyalty-hero .container",
  { opacity: 0, y: 30 },
  {
   opacity: 1,
   y: 0,
   duration: 0.8,
   ease: "power2.out",
   scrollTrigger: {
    trigger: ".loyalty-hero",
    start: "top 80%",
    toggleActions: "play none none reverse",
   },
  }
 );

 // Animate intro section
 const introElements = document.querySelectorAll(
  ".loyalty-intro .container > *"
 );
 introElements.forEach((element, index) => {
  gsap.fromTo(
   element,
   { opacity: 0, y: 30 },
   {
    opacity: 1,
    y: 0,
    duration: 0.7,
    delay: index * 0.2,
    ease: "power2.out",
    scrollTrigger: {
     trigger: element,
     start: "top 85%",
     toggleActions: "play none none reverse",
    },
   }
  );
 });

 // Animate statistics cards
 const statCards = document.querySelectorAll(".loyalty-stat-card");
 statCards.forEach((card, index) => {
  const tl = gsap.timeline({
   scrollTrigger: {
    trigger: card,
    start: "top 85%",
    toggleActions: "play none none reverse",
   },
  });

  // First animate the card appearance
  tl.fromTo(
   card,
   { opacity: 0, y: 30 },
   { opacity: 1, y: 0, duration: 0.7, ease: "power2.out" }
  );

  // Then animate the number
  const numberElement = card.querySelector(".loyalty-stat-number");
  if (numberElement) {
   const targetNumber = parseInt(numberElement.textContent);
   numberElement.textContent = "0%";

   tl.to(
    numberElement,
    {
     duration: 2,
     textContent: targetNumber + "%",
     roundProps: "textContent",
     ease: "power1.inOut",
     snap: { textContent: 1 },
    },
    "-=0.3"
   );
  }

  // Finally animate progress bar
  const progressBar = card.querySelector(".loyalty-progress");
  if (progressBar) {
   const targetWidth = progressBar.getAttribute("data-width");
   tl.fromTo(
    progressBar,
    { width: "0%" },
    { width: targetWidth, duration: 1.5, ease: "power2.inOut" },
    "-=1.7"
   );
  }
 });

 // Animate benefits section
 const benefitsElements = document.querySelectorAll(
  ".loyalty-benefits .container > *"
 );
 benefitsElements.forEach((element, index) => {
  gsap.fromTo(
   element,
   { opacity: 0, y: 30 },
   {
    opacity: 1,
    y: 0,
    duration: 0.7,
    delay: index * 0.2,
    ease: "power2.out",
    scrollTrigger: {
     trigger: element,
     start: "top 85%",
     toggleActions: "play none none reverse",
    },
   }
  );
 });

 // Animate advocacy section
 const advocacyTimeline = gsap.timeline({
  scrollTrigger: {
   trigger: ".loyalty-advocacy",
   start: "top 80%",
   toggleActions: "play none none reverse",
  },
 });

 advocacyTimeline
  .fromTo(
   ".loyalty-advocacy-image",
   { opacity: 0, x: -30 },
   { opacity: 1, x: 0, duration: 0.8, ease: "power2.out" }
  )
  .fromTo(
   ".loyalty-advocacy-content",
   { opacity: 0, x: 30 },
   { opacity: 1, x: 0, duration: 0.8, ease: "power2.out" },
   "-=0.6"
  );

 // Animate final section
 gsap.fromTo(
  ".loyalty-final .container",
  { opacity: 0, y: 30 },
  {
   opacity: 1,
   y: 0,
   duration: 0.8,
   ease: "power2.out",
   scrollTrigger: {
    trigger: ".loyalty-final",
    start: "top 85%",
    toggleActions: "play none none reverse",
   },
  }
 );

 // Animate content text section
 const contentTextElements = document.querySelectorAll(
  ".loyalty-content-text p"
 );
 contentTextElements.forEach((element, index) => {
  gsap.fromTo(
   element,
   { opacity: 0, y: 30 },
   {
    opacity: 1,
    y: 0,
    duration: 0.7,
    delay: index * 0.2,
    ease: "power2.out",
    scrollTrigger: {
     trigger: element,
     start: "top 85%",
     toggleActions: "play none none reverse",
    },
   }
  );
 });
}

// Function to handle popup close
function handlePopupClose(scrollPosition, navigateTo = null) {
 // Check if form was submitted (this will be set by the popup)
 if (hasUserSubmittedForm()) {
  // If form was submitted, refresh the page to show all content
  setTimeout(() => {
   window.location.reload();
  }, 100);
  return;
 }

 // Show sticky menu
 const stickyMenu = document.querySelector(".sticky-menu");
 if (stickyMenu) {
  stickyMenu.classList.add("show");
 }

 // Show loyalty content without hiding main content
 const loyaltyContent = document.getElementById("loyalty-content");
 if (loyaltyContent) {
  // Show content with animation
  loyaltyContent.style.display = "block";

  // Initialize loyalty functionality
  initializeLoyaltyBenefits();
  initializeLoyaltyPopup();
  initializeLoyaltyAnimations();

  // Add fade-in animation class after a brief delay
  setTimeout(() => {
   loyaltyContent.classList.add("fade-in-animation");

   // Scroll to the loyalty section title
   const loyaltySection = document.getElementById("loyalty-section");
   if (loyaltySection) {
    loyaltySection.scrollIntoView({ behavior: "smooth", block: "start" });
   }
  }, 50);
 }

 // Enable content navigation links
 window.enableContentNavigation();

 // If navigateTo parameter is provided, navigate to that section after a delay
 if (navigateTo) {
  setTimeout(() => {
   const targetSection = document.getElementById(navigateTo);
   if (targetSection) {
    targetSection.scrollIntoView({ behavior: "smooth" });
   }
  }, 500);
 }
}

// Function to show environment content
function showEnvironmentContent() {
 // Hide loyalty content
 if (document.getElementById("loyalty-content").style.display === "block") {
  document.getElementById("loyalty-content").style.display = "none";
 }

 // Show environment content
 const environmentContent = document.getElementById("environment-content");
 environmentContent.style.display = "block";

 // Force a reflow to ensure the transition works
 environmentContent.offsetHeight;

 // Add active class to trigger transition
 environmentContent.classList.add("active");

 // Get environment container and scroll container
 const environmentContainer = document.querySelector(".environment-container");
 const scrollContainer = document.querySelector(".scroll-container");

 // Hide the environment container and show the scroll container immediately
 if (environmentContainer && scrollContainer) {
  environmentContainer.style.display = "none";
  scrollContainer.style.display = "flex";

  // Force the window to stay at current scroll position
  window.scrollTo({
   top: window.scrollY,
   left: 0,
   behavior: "auto",
  });

  // Initialize environment sections with animation immediately
  // Setting goToLastSection to false to start from the first section
  initializeEnvironmentSection(false);

  // Ensure document.body has the right overflow style
  document.body.style.overflow = "hidden";
  document.documentElement.style.overflow = "hidden";
 } else {
  console.error("Environment container or scroll container not found");
 }
}

// Function to add a scroll indicator
function addScrollIndicator() {
 const container = document.querySelector(".journey-container");
 if (!container) return;

 // Create a visual scroll indicator
 const scrollIndicator = document.createElement("div");
 scrollIndicator.className = "journey-scroll-indicator";
 scrollIndicator.innerHTML = `
        <div class="journey-scroll-arrow"></div>
        <div class="journey-scroll-text">Scroll to begin</div>
    `;
 container.appendChild(scrollIndicator);

 // Add scroll indicator styles
 const style = document.createElement("style");
 style.textContent = `
        .journey-scroll-indicator {
            position: absolute;
            bottom: 40px;
            left: 50%;
            transform: translateX(-50%);
            text-align: center;
            color: #000;
            z-index: 100;
            animation: fadeIn 0.5s ease-out forwards 0.5s;
            opacity: 0;
        }
        
        .journey-scroll-arrow {
            width: 20px;
            height: 20px;
            border-right: 2px solid #000;
            border-bottom: 2px solid #000;
            transform: rotate(45deg);
            margin: 0 auto 10px;
            animation: bounce 1.5s infinite;
        }
        
        .journey-scroll-text {
            font-family: 'Raleway';
            font-size: 14px;
            opacity: 0.8;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }
        
        @keyframes bounce {
            0%, 100% { transform: rotate(45deg) translate(0, 0); }
            50% { transform: rotate(45deg) translate(0, 10px); }
        }
    `;
 document.head.appendChild(style);
}

// Function to initiate journey animation on scroll
function startJourneyAnimationOnScroll() {
 // Remove event listeners to prevent multiple triggers
 window.removeEventListener("wheel", startJourneyAnimationOnScroll);
 window.removeEventListener("touchmove", startJourneyAnimationOnScroll);

 // Remove any existing scroll indicator
 const existingIndicator = document.querySelector(".journey-scroll-indicator");
 if (existingIndicator) {
  existingIndicator.remove();
 }
}

// Environment section functionality
function initializeEnvironmentSection(goToLastSection = false) {
 console.log(
  "Initializing environment section",
  goToLastSection ? "and going to last section" : ""
 );

 // Get all scroll sections
 const sections = document.querySelectorAll(".scroll-section");
 if (!sections.length) return;

 // Set initial section (default to first, but can be overridden)
 let currentSection = goToLastSection ? sections.length - 1 : 0;
 let isScrolling = false;
 let touchStartY = 0;
 let touchEndY = 0;
 let hasReachedEnd = false; // Track if the user has reached the end of content

 // Set specific background images for each section
 if (sections.length >= 4) {
  sections[0].style.backgroundImage =
   "url('../public/texture-1-background.png')";
  sections[1].style.backgroundImage = "url('../public/env.png')";
  sections[2].style.backgroundImage = "url('../public/exp.png')";
  sections[3].style.backgroundImage = "url('../public/emo.png')";

  const finalSection = document.getElementById("final-scroll");
  if (finalSection) {
   finalSection.style.backgroundImage = "none";
   finalSection.style.backgroundColor = "#FFFFFF";
  }
 }

 // Get the scroll container and set initial transform based on current section
 const scrollContainer = document.querySelector(".scroll-container");
 if (scrollContainer) {
  console.log("Setting initial scroll position to section", currentSection);
  scrollContainer.style.transition = "none"; // Disable transition for immediate positioning
  scrollContainer.style.transform = `translateX(-${currentSection * 100}vw)`;

  // Force reflow to apply the transform immediately
  void scrollContainer.offsetWidth;

  // Re-enable transitions after positioning
  setTimeout(() => {
   scrollContainer.style.transition = "transform 0.8s ease-out";
  }, 50);
 }

 // If going to last section, check if we've reached the end
 if (goToLastSection) {
  checkLastSectionVisibility();
 }

 // Remove any existing event listeners to prevent duplicates
 window.removeEventListener("wheel", handleWheel, { passive: false });
 window.removeEventListener("touchstart", handleTouchStart, { passive: false });
 window.removeEventListener("touchmove", handleTouchMove, { passive: false });
 window.removeEventListener("keydown", handleKeydown);

 // Add event listeners for wheel, touch, and keyboard navigation
 window.addEventListener("wheel", handleWheel, { passive: false });
 window.addEventListener("touchstart", handleTouchStart, { passive: false });
 window.addEventListener("touchmove", handleTouchMove, { passive: false });
 window.addEventListener("keydown", handleKeydown);

 // Make the scrollToSection function available globally
 window.environmentScrollToSection = scrollToSection;

 function checkLastSectionVisibility() {
  const finalSection = sections[sections.length - 1];
  const finalContent = finalSection.querySelector(".final-content");

  if (finalContent) {
   // Look specifically for "Brands that start with the desired emotional outcome" paragraph
   const paragraphs = finalContent.querySelectorAll("p");
   let hasFoundBrandsParagraph = false;

   for (let p of paragraphs) {
    if (
     p.textContent.includes("Brands that start with") &&
     p.textContent.includes("desired emotional outcome")
    ) {
     const rect = p.getBoundingClientRect();
     // If this paragraph is visible in the viewport
     if (rect.top < window.innerHeight) {
      console.log(
       "Found Brands paragraph with desired emotional outcome, marking end reached"
      );
      hasReachedEnd = true;
      hasFoundBrandsParagraph = true;
      break;
     }
    }
   }

   // Fallback check if we didn't find the specific paragraph
   if (!hasFoundBrandsParagraph) {
    const rect = finalContent.getBoundingClientRect();
    const contentHeight = rect.height;
    const visibleHeight = Math.min(
     window.innerHeight - rect.top,
     contentHeight
    );
    const visibleRatio = visibleHeight / contentHeight;

    // If most of the content is visible or we've reached the bottom
    if (visibleRatio > 0.7 || rect.bottom <= window.innerHeight) {
     hasReachedEnd = true;
    }
   }
  } else {
   // If no final content found, set hasReachedEnd to true
   hasReachedEnd = true;
  }
 }

 function handleWheel(e) {
  // Check if we're scrolling within the menu
  const menuContent = document.querySelector(".menu-content");
  if (menuContent && menuContent.contains(e.target)) {
   // Let the menu handle its own scrolling
   return;
  }

  // Check if environment section is still visible/active
  const environmentContent = document.getElementById("environment-content");
  if (
   !environmentContent ||
   environmentContent.style.display === "none" ||
   !environmentContent.classList.contains("active")
  ) {
   // Clean up listeners if environment section is no longer active
   window.removeEventListener("wheel", handleWheel, { passive: false });
   document.removeEventListener("wheel", handleWheel, { passive: false });
   window.removeEventListener("touchstart", handleTouchStart, {
    passive: false,
   });
   window.removeEventListener("touchmove", handleTouchMove, { passive: false });
   window.removeEventListener("keydown", handleKeydown);

   // Restore normal scrolling
   document.body.style.overflow = "auto";
   document.documentElement.style.overflow = "auto";
   document.body.style.position = "";
   document.documentElement.style.position = "";
   return;
  }

  e.preventDefault();
  if (isScrolling) return;

  // Rest of the environment section wheel handling...
  const delta = Math.abs(e.deltaY) >= Math.abs(e.deltaX) ? e.deltaY : e.deltaX;

  // Special case: If at the first section and scrolling up, go back to previous page
  if (currentSection === 0 && delta < -20) {
   console.log(
    "At first environment section and scrolling up, returning to previous page"
   );
   // Hide environment content
   const environmentContent = document.getElementById("environment-content");
   if (environmentContent) {
    environmentContent.style.display = "none";
    environmentContent.classList.remove("active");
   }

   // If we came from engagement content, we should return to the end of loyalty content
   const cameFromEngagement = window.cameFromEngagement;

   // Reset the flag
   window.cameFromEngagement = false;

   // Show loyalty content again
   const loyaltyContent = document.getElementById("loyalty-content");
   if (loyaltyContent) {
    loyaltyContent.style.display = "block";

    // Re-enable scrolling in the loyalty content
    restoreMainScrolling();

    // Remove event listeners from environment section to prevent conflicts
    window.removeEventListener("wheel", handleWheel, { passive: false });
    window.removeEventListener("touchstart", handleTouchStart, {
     passive: false,
    });
    window.removeEventListener("touchmove", handleTouchMove, {
     passive: false,
    });
    window.removeEventListener("keydown", handleKeydown);

    // Re-initialize loyalty functionality and scroll to appropriate section
    setTimeout(() => {
     initializeLoyaltyAnimations();

     // If we came from engagement, scroll to bottom of loyalty content
     if (cameFromEngagement) {
      const loyaltyBottomSection = document.querySelector(
       ".loyalty-bottom-section"
      );
      if (loyaltyBottomSection) {
       loyaltyBottomSection.scrollIntoView({ behavior: "smooth" });
      }
     }

     // Add scroll event listeners for the main document
     window.addEventListener(
      "wheel",
      (e) => {
       // Regular document scrolling - don't prevent default
      },
      { passive: true }
     );
    }, 100);
   } else {
    // If loyalty content doesn't exist, show the main content and intro
    const mainContent = document.querySelector(".main-content");
    const introSection = document.querySelector(".introduction-section");
    if (mainContent) mainContent.style.display = "block";
    if (introSection) introSection.style.display = "block";

    // Re-enable scrolling
    restoreMainScrolling();

    // Remove event listeners from environment section
    window.removeEventListener("wheel", handleWheel, { passive: false });
    window.removeEventListener("touchstart", handleTouchStart, {
     passive: false,
    });
    window.removeEventListener("touchmove", handleTouchMove, {
     passive: false,
    });
    window.removeEventListener("keydown", handleKeydown);
   }
   return;
  }

  // Special check for paragraphs in final section
  if (currentSection === sections.length - 1) {
   const finalSection = sections[sections.length - 1];
   const finalContent = finalSection.querySelector(".final-content");

   if (finalContent) {
    const paragraphs = finalContent.querySelectorAll("p");

    // Check specifically for the paragraph with "Brands that start with the desired emotional outcome"
    for (let p of paragraphs) {
     if (
      p.textContent.includes("Brands that start with") &&
      p.textContent.includes("desired emotional outcome")
     ) {
      const rect = p.getBoundingClientRect();

      // If paragraph is visible and user is scrolling down, go to engagement
      if (rect.top < window.innerHeight && delta > 10) {
       console.log(
        "Scrolling down detected on Brands paragraph with desired emotional outcome, loading engagement"
       );
       loadEngagementContent();
       return;
      }

      // If paragraph is visible and user is scrolling up, go to previous section
      if (rect.top < window.innerHeight && delta < -20) {
       console.log(
        "Scrolling up detected on Brands paragraph, navigating to previous section"
       );
       scrollToSection(currentSection - 1);
       return;
      }
     }
    }
   }

   // Check if we've reached the end of content
   if (!hasReachedEnd) {
    checkLastSectionVisibility();
   }

   // If we've reached the end and user scrolls down, transition to engagement content
   if (hasReachedEnd && delta > 20) {
    console.log("End reached and scrolling down detected, loading engagement");
    loadEngagementContent();
    return;
   }

   // If we've reached the end and user scrolls up, go to previous section
   if (hasReachedEnd && delta < -20) {
    console.log(
     "End reached and scrolling up detected, navigating to previous section"
    );
    scrollToSection(currentSection - 1);
    return;
   }
  }

  // Log detailed wheel event info for debugging
  console.log(
   "Wheel event - deltaY:",
   e.deltaY,
   "deltaX:",
   e.deltaX,
   "combined delta:",
   delta
  );

  // Normal section navigation - reduced threshold for better detection
  if (delta > 20) {
   // Scroll to next section
   console.log(
    "Scrolling down, navigating to next section: " + (currentSection + 1)
   );
   scrollToSection(currentSection + 1);
  } else if (delta < -20) {
   // Scroll to previous section - making sure this works with lower threshold
   console.log(
    "Scrolling up, navigating to previous section: " + (currentSection - 1)
   );
   scrollToSection(currentSection - 1);
  }
 }

 function handleTouchStart(e) {
  touchStartY = e.touches[0].clientY;
  touchEndY = touchStartY;
 }

 function handleTouchMove(e) {
  e.preventDefault();
  if (isScrolling) return;

  touchEndY = e.touches[0].clientY;
  const deltaY = touchEndY - touchStartY;

  // Special case: If at the first section and swiping up, go back to previous page
  if (currentSection === 0 && deltaY > 50) {
   console.log(
    "At first environment section and swiping up, returning to previous page"
   );
   // Hide environment content
   const environmentContent = document.getElementById("environment-content");
   if (environmentContent) {
    environmentContent.style.display = "none";
    environmentContent.classList.remove("active");
   }

   // Check if we came from engagement content
   const cameFromEngagement = window.cameFromEngagement;

   // Reset the flag
   window.cameFromEngagement = false;

   // Show loyalty content again
   const loyaltyContent = document.getElementById("loyalty-content");
   if (loyaltyContent) {
    loyaltyContent.style.display = "block";

    // Re-enable scrolling in the loyalty content
    restoreMainScrolling();

    // Remove event listeners from environment section to prevent conflicts
    window.removeEventListener("wheel", handleWheel, { passive: false });
    window.removeEventListener("touchstart", handleTouchStart, {
     passive: false,
    });
    window.removeEventListener("touchmove", handleTouchMove, {
     passive: false,
    });
    window.removeEventListener("keydown", handleKeydown);

    // Re-initialize loyalty functionality
    setTimeout(() => {
     initializeLoyaltyAnimations();

     // If we came from engagement, scroll to bottom of loyalty content
     if (cameFromEngagement) {
      const loyaltyBottomSection = document.querySelector(
       ".loyalty-bottom-section"
      );
      if (loyaltyBottomSection) {
       loyaltyBottomSection.scrollIntoView({ behavior: "smooth" });
      }
     }
    }, 100);
   } else {
    // If loyalty content doesn't exist, show the main content and intro
    const mainContent = document.querySelector(".main-content");
    const introSection = document.querySelector(".introduction-section");
    if (mainContent) mainContent.style.display = "block";
    if (introSection) introSection.style.display = "block";

    // Re-enable scrolling
    restoreMainScrolling();

    // Remove event listeners from environment section
    window.removeEventListener("wheel", handleWheel, { passive: false });
    window.removeEventListener("touchstart", handleTouchStart, {
     passive: false,
    });
    window.removeEventListener("touchmove", handleTouchMove, {
     passive: false,
    });
    window.removeEventListener("keydown", handleKeydown);
   }
   return;
  }

  // Special check for paragraphs in final section
  if (currentSection === sections.length - 1) {
   const finalSection = sections[sections.length - 1];
   const finalContent = finalSection.querySelector(".final-content");

   if (finalContent) {
    const paragraphs = finalContent.querySelectorAll("p");

    // Check specifically for the paragraph with "Brands that start with the desired emotional outcome"
    for (let p of paragraphs) {
     if (
      p.textContent.includes("Brands that start with") &&
      p.textContent.includes("desired emotional outcome")
     ) {
      const rect = p.getBoundingClientRect();

      // If paragraph is visible and user is swiping down (negative deltaY), go to engagement
      if (rect.top < window.innerHeight && deltaY < -10) {
       console.log(
        "Touch swipe down detected on Brands paragraph with desired emotional outcome, loading engagement"
       );
       loadEngagementContent();
       return;
      }

      // If paragraph is visible and user is swiping up (positive deltaY), go to previous section
      if (rect.top < window.innerHeight && deltaY > 50) {
       console.log(
        "Touch swipe up detected on Brands paragraph, navigating to previous section"
       );
       scrollToSection(currentSection - 1);
       return;
      }
     }
    }
   }

   // Check if we've reached the end of content
   if (!hasReachedEnd) {
    checkLastSectionVisibility();
   }

   // If we've reached the end and user swipes down (negative deltaY), transition to engagement content
   if (hasReachedEnd && deltaY < -50) {
    console.log(
     "End reached and touch swipe down detected, loading engagement"
    );
    loadEngagementContent();
    return;
   }

   // If we've reached the end and user swipes up (positive deltaY), go to previous section
   if (hasReachedEnd && deltaY > 50) {
    console.log(
     "End reached and touch swipe up detected, navigating to previous section"
    );
    scrollToSection(currentSection - 1);
    return;
   }
  }

  // Normal section navigation
  // Note: For touch events, positive deltaY means swiping UP (finger moving up)
  if (deltaY < -50) {
   // Swipe down - go to next section
   console.log(
    "Swipe down detected, navigating to next section: " + (currentSection + 1)
   );
   scrollToSection(currentSection + 1);
   touchStartY = touchEndY; // Reset to prevent multiple triggers
  } else if (deltaY > 50) {
   // Swipe up - go to previous section
   console.log(
    "Swipe up detected, navigating to previous section: " + (currentSection - 1)
   );
   scrollToSection(currentSection - 1);
   touchStartY = touchEndY; // Reset to prevent multiple triggers
  }
 }

 function handleKeydown(e) {
  if (isScrolling) return;

  // Special case: If at the first section and pressing up or left arrow, go back to previous page
  if (currentSection === 0 && (e.key === "ArrowLeft" || e.key === "ArrowUp")) {
   console.log(
    "At first environment section and pressing up/left arrow, returning to previous page"
   );
   e.preventDefault();

   // Hide environment content
   const environmentContent = document.getElementById("environment-content");
   if (environmentContent) {
    environmentContent.style.display = "none";
    environmentContent.classList.remove("active");
   }

   // Check if we came from engagement content
   const cameFromEngagement = window.cameFromEngagement;

   // Reset the flag
   window.cameFromEngagement = false;

   // Show loyalty content again
   const loyaltyContent = document.getElementById("loyalty-content");
   if (loyaltyContent) {
    loyaltyContent.style.display = "block";

    // Re-enable scrolling in the loyalty content
    restoreMainScrolling();

    // Remove event listeners from environment section to prevent conflicts
    window.removeEventListener("wheel", handleWheel, { passive: false });
    window.removeEventListener("touchstart", handleTouchStart, {
     passive: false,
    });
    window.removeEventListener("touchmove", handleTouchMove, {
     passive: false,
    });
    window.removeEventListener("keydown", handleKeydown);

    // Re-initialize loyalty functionality
    setTimeout(() => {
     initializeLoyaltyAnimations();

     // If we came from engagement, scroll to bottom of loyalty content
     if (cameFromEngagement) {
      const loyaltyBottomSection = document.querySelector(
       ".loyalty-bottom-section"
      );
      if (loyaltyBottomSection) {
       loyaltyBottomSection.scrollIntoView({ behavior: "smooth" });
      }
     }
    }, 100);
   } else {
    // If loyalty content doesn't exist, show the main content and intro
    const mainContent = document.querySelector(".main-content");
    const introSection = document.querySelector(".introduction-section");
    if (mainContent) mainContent.style.display = "block";
    if (introSection) introSection.style.display = "block";

    // Re-enable scrolling
    restoreMainScrolling();

    // Remove event listeners from environment section
    window.removeEventListener("wheel", handleWheel, { passive: false });
    window.removeEventListener("touchstart", handleTouchStart, {
     passive: false,
    });
    window.removeEventListener("touchmove", handleTouchMove, {
     passive: false,
    });
    window.removeEventListener("keydown", handleKeydown);
   }
   return;
  }

  // If on last section, handle special cases
  if (currentSection === sections.length - 1) {
   // Check if we've reached the end of content
   if (!hasReachedEnd) {
    checkLastSectionVisibility();
   }

   // Get brands paragraph to check visibility
   const finalSection = sections[sections.length - 1];
   const finalContent = finalSection.querySelector(".final-content");
   let brandsParagraphVisible = false;

   if (finalContent) {
    const paragraphs = finalContent.querySelectorAll("p");
    for (let p of paragraphs) {
     if (
      p.textContent.includes("Brands that start with") &&
      p.textContent.includes("desired emotional outcome")
     ) {
      const rect = p.getBoundingClientRect();
      if (rect.top < window.innerHeight) {
       brandsParagraphVisible = true;
       break;
      }
     }
    }
   }

   // If brands paragraph is visible or we've reached the end
   if (brandsParagraphVisible || hasReachedEnd) {
    // If right or down arrow pressed, go to engagement content
    if (e.key === "ArrowRight" || e.key === "ArrowDown") {
     console.log(
      "At final section with brands paragraph visible, navigating to engagement"
     );
     loadEngagementContent();
     e.preventDefault();
     return;
    }

    // If left or up arrow pressed, go to previous section
    if (e.key === "ArrowLeft" || e.key === "ArrowUp") {
     console.log(
      "At final section with brands paragraph visible, navigating to previous section"
     );
     scrollToSection(currentSection - 1);
     e.preventDefault();
     return;
    }
   }
  }

  if (e.key === "ArrowRight" || e.key === "ArrowDown") {
   console.log(
    "Arrow right/down pressed, navigating to next section: " +
     (currentSection + 1)
   );
   scrollToSection(currentSection + 1);
   e.preventDefault();
  } else if (e.key === "ArrowLeft" || e.key === "ArrowUp") {
   console.log(
    "Arrow left/up pressed, navigating to previous section: " +
     (currentSection - 1)
   );
   scrollToSection(currentSection - 1);
   e.preventDefault();
  }
 }

 function scrollToSection(index) {
  if (isScrolling) return;
  isScrolling = true;

  console.log(
   "Trying to scroll to section:",
   index,
   "from current section:",
   currentSection
  );

  // Check if trying to navigate past the end
  if (index >= sections.length) {
   // If we're at the last section and have reached the end
   if (currentSection === sections.length - 1) {
    // Check if we've already determined we're at the end
    if (hasReachedEnd) {
     // Load engagement content
     loadEngagementContent();
     return;
    } else {
     // Check if we've reached the end
     checkLastSectionVisibility();
     if (hasReachedEnd) {
      // Load engagement content
      loadEngagementContent();
      return;
     }
    }
   }
   // Otherwise just go to the last section
   index = sections.length - 1;
  }

  // Handle negative index (going before first section)
  if (index < 0) {
   console.log("Attempting to navigate before first section - setting to 0");
   index = 0;
  }

  console.log("Navigating to section:", index);
  currentSection = index;

  // Force scroll container to move horizontally to the new section
  if (scrollContainer) {
   scrollContainer.style.transition = "transform 0.8s ease-out";
   scrollContainer.style.transform = `translateX(-${currentSection * 100}vw)`;
  }

  // Note: Removed automatic check for last section visibility
  // Users can now stay on the final section without automatic transitions

  // Reset scrolling flag after animation
  setTimeout(() => {
   isScrolling = false;
  }, 800);
 }
}

// Function to load engagement content
function loadEngagementContent(scrollToEnd = false) {
 console.log(
  "Loading engagement content",
  scrollToEnd ? "and scrolling to end" : ""
 );

 // Reset the cameFromEngagement flag since we're now going to engagement content
 window.cameFromEngagement = false;

 // Clean up any existing wheel event listeners to prevent conflicts
 const cleanupWheelListeners = () => {
  // Try to remove any non-passive wheel listeners that might be blocking scrolling
  try {
   // Remove wheel event listeners from window and document
   window.removeEventListener("wheel", handleWheel, { passive: false });
   document.removeEventListener("wheel", handleWheel, { passive: false });

   // Try with different options
   window.removeEventListener("wheel", handleWheel);
   document.removeEventListener("wheel", handleWheel);

   // Try to remove from environment section specifically
   const environmentSection = document.querySelector(".environment-section");
   if (environmentSection) {
    environmentSection.removeEventListener("wheel", handleWheel, {
     passive: false,
    });
    environmentSection.removeEventListener("wheel", handleWheel);
   }

   // Remove handleEnvironmentWheel if it exists
   try {
    window.removeEventListener("wheel", handleEnvironmentWheel, {
     passive: false,
    });
    document.removeEventListener("wheel", handleEnvironmentWheel, {
     passive: false,
    });
    window.removeEventListener("wheel", handleEnvironmentWheel);
    document.removeEventListener("wheel", handleEnvironmentWheel);
   } catch (err) {
    // Ignore if function doesn't exist
   }
  } catch (e) {
   /* Ignore errors if listener doesn't exist */
  }

  // Prevent scrolling on the main document
  document.body.style.overflow = "hidden";
  document.documentElement.style.overflow = "hidden";
  document.body.style.position = "fixed";
  document.documentElement.style.position = "fixed";
  document.body.style.width = "100%";
  document.body.style.height = "100%";

  // Add a passive wheel listener to ensure scrolling works within iframe
  window.addEventListener(
   "wheel",
   (e) => {
    // This is a passive wheel handler that allows normal scrolling in iframe
   },
   { passive: true }
  );
 };

 // Clean up before loading engagement content
 cleanupWheelListeners();

 // Hide the environment content instantly without animation
 const environmentContent = document.getElementById("environment-content");
 if (environmentContent) {
  environmentContent.style.display = "none";
  environmentContent.classList.remove("active");
 }

 // Hide loyalty content if visible
 const loyaltyContent = document.getElementById("loyalty-content");
 if (loyaltyContent) {
  loyaltyContent.style.display = "none";
 }

 // Create a container for the engagement content if it doesn't exist
 let engagementContainer = document.getElementById("engagement-content");
 if (!engagementContainer) {
  engagementContainer = document.createElement("div");
  engagementContainer.id = "engagement-content";
  engagementContainer.style.width = "100%";
  engagementContainer.style.height = "100vh";
  engagementContainer.style.opacity = "1";
  document.querySelector(".container").appendChild(engagementContainer);
 } else {
  engagementContainer.style.opacity = "1";
 }

 // Create and add the iframe
 const iframe = document.createElement("iframe");
 iframe.src = "engagement/engagement.html";
 iframe.style.width = "100%";
 iframe.style.height = "100%";
 iframe.style.border = "none";
 iframe.style.position = "fixed";
 iframe.style.top = "0";
 iframe.style.left = "0";
 iframe.style.zIndex = "1000";
 iframe.style.backgroundColor = "#FFFFFF";
 iframe.style.overflow = "auto"; // Ensure iframe has its own scrollbar

 // Create a message listener to wait for the engagement iframe to load
 const handleMessage = (event) => {
  if (event.data && event.data.type === "engagementLoaded") {
   // Remove this listener once we've processed the message
   window.removeEventListener("message", handleMessage);

   // If scrollToEnd is true, scroll to the conclusion section
   if (scrollToEnd && iframe.contentWindow) {
    setTimeout(() => {
     iframe.contentWindow.postMessage(
      {
       type: "scrollToSection",
       sectionId: "end",
      },
      "*"
     );
    }, 500); // Add delay to ensure content is ready
   }

   // Ensure scrolling is properly initialized in the iframe
   if (iframe.contentWindow) {
    // Send a message to initialize scrolling in the iframe
    iframe.contentWindow.postMessage(
     {
      type: "initializeScrolling",
     },
     "*"
    );
   }
  }
 };

 // Add the message listener
 window.addEventListener("message", handleMessage);

 // Clear previous content and add the iframe
 engagementContainer.innerHTML = "";
 engagementContainer.appendChild(iframe);

 // Show the container immediately
 engagementContainer.style.display = "block";

 // Set parent document body to prevent scrolling
 document.body.style.overflow = "hidden";
 document.documentElement.style.overflow = "hidden";

 // Scroll to top to ensure proper viewing
 window.scrollTo({
  top: 0,
  left: 0,
  behavior: "instant",
 });

 // Run cleanup again after a delay to ensure scrolling works
 setTimeout(cleanupWheelListeners, 500);

 // Add an additional cleanup after a longer delay to ensure scrolling is fully initialized
 setTimeout(() => {
  // Force iframe to focus to ensure it captures keyboard events
  iframe.focus();

  // Ensure iframe has proper overflow settings
  if (iframe.contentDocument) {
   iframe.contentDocument.body.style.overflow = "auto";
   iframe.contentDocument.documentElement.style.overflow = "auto";
  }
 }, 1000);
}

// Helper function to load engagement content directly
function loadEngagementDirect() {
 console.log("Loading engagement content directly without delay");

 // Reset the cameFromEngagement flag since we're now going to engagement content
 window.cameFromEngagement = false;

 // Clean up any existing wheel event listeners to prevent conflicts
 const cleanupWheelListeners = () => {
  // Try to remove any non-passive wheel listeners that might be blocking scrolling
  try {
   // Remove wheel event listeners from window and document
   window.removeEventListener("wheel", handleWheel, { passive: false });
   document.removeEventListener("wheel", handleWheel, { passive: false });

   // Try with different options
   window.removeEventListener("wheel", handleWheel);
   document.removeEventListener("wheel", handleWheel);

   // Try to remove from environment section specifically
   const environmentSection = document.querySelector(".environment-section");
   if (environmentSection) {
    environmentSection.removeEventListener("wheel", handleWheel, {
     passive: false,
    });
    environmentSection.removeEventListener("wheel", handleWheel);
   }

   // Remove handleEnvironmentWheel if it exists
   try {
    window.removeEventListener("wheel", handleEnvironmentWheel, {
     passive: false,
    });
    document.removeEventListener("wheel", handleEnvironmentWheel, {
     passive: false,
    });
    window.removeEventListener("wheel", handleEnvironmentWheel);
    document.removeEventListener("wheel", handleEnvironmentWheel);
   } catch (err) {
    // Ignore if function doesn't exist
   }
  } catch (e) {
   /* Ignore errors if listener doesn't exist */
  }

  // Prevent scrolling on the main document
  document.body.style.overflow = "hidden";
  document.documentElement.style.overflow = "hidden";
  document.body.style.position = "fixed";
  document.documentElement.style.position = "fixed";
  document.body.style.width = "100%";
  document.body.style.height = "100%";

  // Add a passive wheel listener to ensure scrolling works within iframe
  window.addEventListener(
   "wheel",
   (e) => {
    // This is a passive wheel handler that allows normal scrolling in iframe
   },
   { passive: true }
  );
 };

 // Clean up before loading engagement content
 cleanupWheelListeners();

 // Create a container for the engagement content if it doesn't exist
 let engagementContainer = document.getElementById("engagement-content");
 if (!engagementContainer) {
  engagementContainer = document.createElement("div");
  engagementContainer.id = "engagement-content";
  engagementContainer.style.width = "100%";
  engagementContainer.style.height = "100vh";
  // Make it immediately visible
  engagementContainer.style.opacity = "1";
  document.querySelector(".container").appendChild(engagementContainer);
 } else {
  engagementContainer.style.opacity = "1";
 }

 // Create and add the iframe
 const iframe = document.createElement("iframe");
 iframe.src = "engagement/engagement.html";
 iframe.style.width = "100%";
 iframe.style.height = "100%";
 iframe.style.border = "none";
 iframe.style.position = "fixed";
 iframe.style.top = "0";
 iframe.style.left = "0";
 iframe.style.zIndex = "1000";
 iframe.style.backgroundColor = "#FFFFFF"; // Add white background to prevent flashing

 // Clear previous content and add the iframe immediately
 engagementContainer.innerHTML = "";
 engagementContainer.appendChild(iframe);

 // Show the container immediately
 engagementContainer.style.display = "block";

 // Scroll to top to ensure proper viewing
 window.scrollTo({
  top: 0,
  left: 0,
  behavior: "instant",
 });

 // Run cleanup again after a delay to ensure scrolling works
 setTimeout(cleanupWheelListeners, 500);
}

// Add styles for scroll indicator
const style = document.createElement("style");
style.textContent = `
    .scroll-indicator {
        position: absolute;
        bottom: 40px;
        left: 50%;
        transform: translateX(-50%);
        text-align: center;
        color: #000;
        opacity: 0;
        animation: fadeIn 0.5s ease-out forwards;
    }
    
    .scroll-arrow {
        width: 20px;
        height: 20px;
        border-right: 2px solid #000;
        border-bottom: 2px solid #000;
        transform: rotate(45deg);
        margin: 0 auto 10px;
    }
    
    .scroll-text {
        font-family: 'Raleway';
        font-size: 14px;
        opacity: 0.8;
    }
    
    @keyframes fadeIn {
        from { opacity: 0; }
        to { opacity: 1; }
    }
`;
document.head.appendChild(style);

// Function to restore scrolling in main content
function restoreMainScrolling() {
 console.log("Restoring main scrolling functionality");

 // Ensure document.body has normal scrolling enabled
 document.body.style.overflow = "auto";
 document.documentElement.style.overflow = "auto";

 // Reset fixed positioning we added for engagement content
 document.body.style.position = "";
 document.documentElement.style.position = "";
 document.body.style.width = "";
 document.body.style.height = "";

 // Remove any inline overflow styles that might be interfering
 document.body.style.removeProperty("overflow");
 document.documentElement.style.removeProperty("overflow");

 // Re-add the standard scroll event listener
 window.addEventListener("scroll", handleScroll);

 // Make sure wheel events work properly by explicitly setting passive: true
 // for normal document scrolling
 window.addEventListener(
  "wheel",
  (e) => {
   // This is a passive wheel handler that allows normal scrolling
  },
  { passive: true }
 );

 // Force a scroll event to update positions
 setTimeout(() => {
  window.dispatchEvent(new Event("scroll"));
 }, 100);
}

// Function to set up content navigation links
function setupContentNavigationLinks() {
 // Get the content link elements
 const loyaltyLink = document.getElementById("loyalty-link");
 const advocacyLink = document.getElementById("advocacy-link");
 const awarenessLink = document.getElementById("awareness-link");

 // Initially set them to not navigable (will be enabled after popup closes)
 let navigableLinks = false;

 // Define click handlers
 if (loyaltyLink) {
  loyaltyLink.style.cursor = "pointer";
  loyaltyLink.addEventListener("click", function (e) {
   e.preventDefault();
   if (navigableLinks) {
    // Show loyalty content if not already visible
    if (document.getElementById("loyalty-content").style.display !== "block") {
     showLoyaltyContent();
    }
    // Scroll to loyalty section
    const loyaltySection = document.getElementById("loyalty-section");
    if (loyaltySection) {
     loyaltySection.scrollIntoView({ behavior: "smooth" });
    }
   } else {
    // If links aren't navigable yet (popup not closed), load popup with navigation target
    loadPopup("loyalty-section");
   }
  });
 }

 if (advocacyLink) {
  advocacyLink.style.cursor = "pointer";
  advocacyLink.addEventListener("click", function (e) {
   e.preventDefault();
   if (navigableLinks) {
    // Show loyalty content if not already visible
    if (document.getElementById("loyalty-content").style.display !== "block") {
     showLoyaltyContent();
    }
    // Scroll to advocacy section
    const advocacySection = document.getElementById("advocacy-section");
    if (advocacySection) {
     advocacySection.scrollIntoView({ behavior: "smooth" });
    }
   } else {
    // If links aren't navigable yet (popup not closed), load popup with navigation target
    loadPopup("advocacy-section");
   }
  });
 }

 if (awarenessLink) {
  awarenessLink.style.cursor = "pointer";
  awarenessLink.addEventListener("click", function (e) {
   e.preventDefault();
   if (navigableLinks) {
    // Show loyalty content if not already visible
    if (document.getElementById("loyalty-content").style.display !== "block") {
     showLoyaltyContent();
    }
    // Scroll to awareness section
    const awarenessSection = document.getElementById("awareness-section");
    if (awarenessSection) {
     awarenessSection.scrollIntoView({ behavior: "smooth" });
    }
   } else {
    // If links aren't navigable yet (popup not closed), load popup with navigation target
    loadPopup("awareness-section");
   }
  });
 }

 // Function to enable navigation links (will be called when popup closes)
 window.enableContentNavigation = function () {
  navigableLinks = true;
  window.navigableLinks = true;
 };
}

// Add global message listener for iframe communications
window.addEventListener("message", function (event) {
 // Check origin for security (can be enhanced for production)
 if (event.data) {
  // Handle messages from engagement.html
  if (event.data.type === "navigateToEnvironment") {
   console.log("Received navigateToEnvironment message from engagement iframe");
   hideEngagementShowEnvironment();
  }
 }
});

// Function to hide engagement content and show environment content
function hideEngagementShowEnvironment() {
 console.log("Hiding engagement content and showing environment content");

 // Store that we came from engagement content
 window.cameFromEngagement = true;

 // Hide the engagement content
 const engagementContent = document.getElementById("engagement-content");
 if (engagementContent) {
  engagementContent.style.display = "none";
 }

 // Show the environment content and make it active
 const environmentContent = document.getElementById("environment-content");
 if (environmentContent) {
  // Make sure the scroll container is displayed and environment container is hidden
  const environmentContainer = document.querySelector(".environment-container");
  const scrollContainer = document.querySelector(".scroll-container");

  if (environmentContainer && scrollContainer) {
   environmentContainer.style.display = "none";
   scrollContainer.style.display = "flex";
  }

  environmentContent.style.display = "block";
  environmentContent.classList.add("active");

  // Force document body to have overflow hidden to prevent page scrolling
  document.body.style.overflow = "hidden";
  document.documentElement.style.overflow = "hidden";

  // Initialize the environment section to ensure scroll functionality
  setTimeout(() => {
   // Make sure regular page scrolling is disabled
   window.scrollTo({
    top: 0,
    left: 0,
    behavior: "instant",
   });

   // Initialize environment section with goToLastSection = true
   initializeEnvironmentSection(true);

   // No need to manually call scrollToSection or set transform since
   // initializeEnvironmentSection with goToLastSection=true will handle it
  }, 100);
 }
}

// Initialize sticky menu when DOM is loaded
document.addEventListener("DOMContentLoaded", () => {
 console.log("Initializing sticky menu");
 initializeStickyMenu();
});

// Function to initialize the sticky menu
function initializeStickyMenu() {
 console.log("Initializing sticky menu");

 // Get menu elements
 const stickyMenu = document.querySelector(".sticky-menu");
 const menuToggle = document.querySelector(".menu-toggle");
 const menuClose = document.querySelector(".menu-close");
 const menuContent = document.querySelector(".menu-content");

 console.log("Menu elements found:", {
  stickyMenu: stickyMenu,
  menuToggle: menuToggle,
  menuClose: menuClose,
  menuContent: menuContent,
 });

 // Show sticky menu immediately when initialized
 if (stickyMenu) {
  stickyMenu.classList.add("show");
  console.log("Sticky menu shown");
 }

 // Basic toggle functionality
 if (menuToggle && menuContent) {
  // Remove any existing click listeners first
  const newMenuToggle = menuToggle.cloneNode(true);
  menuToggle.parentNode.replaceChild(newMenuToggle, menuToggle);

  // Add new click listener
  newMenuToggle.addEventListener("click", function (e) {
   console.log("Menu toggle clicked");
   e.preventDefault();
   e.stopPropagation();
   if (menuContent.classList.contains("open")) {
    // Close menu
    menuContent.classList.remove("open");
    document.body.classList.remove("menu-open");
    console.log("Menu closed");
   } else {
    // Open menu
    menuContent.classList.add("open");
    document.body.classList.add("menu-open");
    console.log("Menu opened");
   }
  });
 }

 // Close button functionality
 if (menuClose && menuContent) {
  // Remove any existing click listeners first
  const newMenuClose = menuClose.cloneNode(true);
  menuClose.parentNode.replaceChild(newMenuClose, menuClose);

  // Add new click listener
  newMenuClose.addEventListener("click", function (e) {
   console.log("Close button clicked");
   e.preventDefault();
   e.stopPropagation();
   menuContent.classList.remove("open");
   document.body.classList.remove("menu-open");
  });
 }

 // Close menu when clicking outside
 document.addEventListener("click", function (e) {
  if (menuContent && menuContent.classList.contains("open")) {
   const currentMenuToggle = document.querySelector(".menu-toggle");
   if (
    !menuContent.contains(e.target) &&
    (!currentMenuToggle || !currentMenuToggle.contains(e.target))
   ) {
    menuContent.classList.remove("open");
    document.body.classList.remove("menu-open");
   }
  }
 });

 // Prevent menu from closing when clicking inside
 if (menuContent) {
  menuContent.addEventListener("click", function (e) {
   e.stopPropagation();
  });
 }

 // Handle menu items
 const menuItems = document.querySelectorAll(".menu-item-title");
 const journeyItems = document.querySelectorAll(".menu-journey-item");

 // Add click handler for the awareness subtitle
 const awarenessSubtitle = document.getElementById("menu-awareness-subtitle");
 if (awarenessSubtitle) {
  awarenessSubtitle.addEventListener("click", () => {
   // Use the same target as menu-awareness-link
   let targetId = "awareness-section";

   // Clean up any existing wheel event listeners to prevent conflicts
   const cleanupExistingListeners = () => {
    try {
     // Remove any wheel event listeners that might exist
     window.removeEventListener("wheel", handleWheel, { passive: false });
     document.removeEventListener("wheel", handleWheel, { passive: false });

     // Also try removing with different options
     window.removeEventListener("wheel", handleWheel);
     document.removeEventListener("wheel", handleWheel);

     // Remove event listeners from environment and engagement sections
     const environmentSection = document.querySelector(".environment-section");
     if (environmentSection) {
      environmentSection.removeEventListener("wheel", handleWheel, {
       passive: false,
      });
      environmentSection.removeEventListener("wheel", handleWheel);
     }
    } catch (e) {
     /* Ignore errors if listener doesn't exist */
    }

    // Reset scrolling properties on the body and html elements
    document.body.style.overflow = "auto";
    document.documentElement.style.overflow = "auto";
    document.body.style.position = "";
    document.documentElement.style.position = "";

    // Ensure wheel events work normally for all scrolling
    document.addEventListener(
     "wheel",
     function wheelHandler(e) {
      // This is just to ensure normal scrolling behavior is restored
      // The event will propagate naturally
     },
     { passive: true }
    );
   };

   // Run cleanup before proceeding
   cleanupExistingListeners();

   // Check if we're in engagement content
   const engagementContent = document.getElementById("engagement-content");
   const environmentContent = document.getElementById("environment-content");

   // Hide both engagement and environment content if they're visible
   if (engagementContent) {
    engagementContent.style.display = "none";
   }
   if (environmentContent) {
    environmentContent.style.display = "none";
    environmentContent.classList.remove("active");

    // Use our global cleanup function
    cleanupEnvironmentListeners();
   }

   // Show loyalty content and navigate
   const loyaltyContent = document.getElementById("loyalty-content");
   if (loyaltyContent) {
    loyaltyContent.style.display = "block";

    // Re-enable scrolling
    document.body.style.overflow = "auto";
    document.documentElement.style.overflow = "auto";

    // Remove any fixed positioning that might interfere
    document.body.style.position = "";
    document.documentElement.style.position = "";

    // Wait for content to be visible then scroll
    setTimeout(() => {
     const targetElement = document.getElementById(targetId);
     if (targetElement) {
      // First scroll to top to ensure consistent behavior
      window.scrollTo(0, 0);

      // Then scroll to target section
      setTimeout(() => {
       targetElement.scrollIntoView({ behavior: "smooth" });

       // Reinitialize any necessary animations or functionality
       if (typeof initializeLoyaltyAnimations === "function") {
        initializeLoyaltyAnimations();
       }
      }, 100);
     }
     // Ensure scrolling works after navigation
     cleanupExistingListeners();
    }, 100);
   }

   // Close the menu
   const menuContent = document.querySelector(".menu-content");
   if (menuContent) {
    menuContent.classList.remove("open");
    document.body.classList.remove("menu-open");
   }
  });
 }

 menuItems.forEach((item) => {
  item.addEventListener("click", () => {
   const menuId = item.id;
   let targetId;

   // If this is the "Creating a sustainable Customer Engagement Strategy" item
   if (
    item.textContent.trim() ===
    "Creating a sustainable Customer Engagement Strategy"
   ) {
    // Hide any visible content
    const environmentContent = document.getElementById("environment-content");
    if (environmentContent) {
     environmentContent.style.display = "none";
     environmentContent.classList.remove("active");
    }

    const loyaltyContent = document.getElementById("loyalty-content");
    if (loyaltyContent) {
     loyaltyContent.style.display = "none";
    }

    // Create engagement container if it doesn't exist
    let engagementContainer = document.getElementById("engagement-content");
    if (!engagementContainer) {
     engagementContainer = document.createElement("div");
     engagementContainer.id = "engagement-content";
     engagementContainer.style.width = "100%";
     engagementContainer.style.height = "100vh";
     document.querySelector(".container").appendChild(engagementContainer);
    }

    // Set the iframe source directly to the ending section
    const iframe = document.createElement("iframe");
    iframe.src = "engagement/engagement.html#end";
    iframe.style.width = "100%";
    iframe.style.height = "100%";
    iframe.style.border = "none";
    iframe.style.position = "fixed";
    iframe.style.top = "0";
    iframe.style.left = "0";
    iframe.style.zIndex = "1000";
    iframe.style.backgroundColor = "#FFFFFF";

    // Add the iframe load handler to handle navigation properly
    iframe.onload = function () {
     console.log("Iframe loaded with #end hash successfully");

     // Force a refresh after a short delay to ensure hash fragment is processed
     setTimeout(() => {
      // If the hash fragment doesn't work, try direct message
      iframe.contentWindow.postMessage(
       {
        type: "scrollToSection",
        sectionId: "end",
       },
       "*"
      );

      // Initialize mouse wheel handling for proper up-scrolling
      iframe.contentWindow.postMessage(
       {
        type: "initializeScrolling",
       },
       "*"
      );
     }, 500);
    };

    // Clear container and add iframe
    engagementContainer.innerHTML = "";
    engagementContainer.appendChild(iframe);
    engagementContainer.style.display = "block";

    // Add the iframe load handler
    iframe.onload = function () {
     console.log("Iframe loaded successfully");
    };

    // Close the menu
    document.querySelector(".sticky-menu")?.classList.remove("menu-open");

    // Clean up any existing wheel event listeners
    try {
     window.removeEventListener("wheel", handleWheel, { passive: false });
    } catch (e) {
     /* Ignore errors if listener doesn't exist */
    }

    return;
   }

   // Clean up any existing wheel event listeners to prevent conflicts
   const cleanupExistingListeners = () => {
    try {
     // Remove any wheel event listeners that might exist
     window.removeEventListener("wheel", handleWheel, { passive: false });
     document.removeEventListener("wheel", handleWheel, { passive: false });

     // Also try removing with different options
     window.removeEventListener("wheel", handleWheel);
     document.removeEventListener("wheel", handleWheel);

     // Remove event listeners from environment and engagement sections
     const environmentSection = document.querySelector(".environment-section");
     if (environmentSection) {
      environmentSection.removeEventListener("wheel", handleWheel, {
       passive: false,
      });
      environmentSection.removeEventListener("wheel", handleWheel);
     }
    } catch (e) {
     /* Ignore errors if listener doesn't exist */
    }

    // Reset scrolling properties on the body and html elements
    document.body.style.overflow = "auto";
    document.documentElement.style.overflow = "auto";
    document.body.style.position = "";
    document.documentElement.style.position = "";

    // Ensure wheel events work normally for all scrolling
    document.addEventListener(
     "wheel",
     function wheelHandler(e) {
      // This is just to ensure normal scrolling behavior is restored
      // The event will propagate naturally
     },
     { passive: true }
    );
   };

   // Run cleanup before proceeding
   cleanupExistingListeners();

   // Map menu items to their corresponding sections
   switch (menuId) {
    case "menu-loyalty-link":
     targetId = "loyalty-section";
     break;
    case "menu-advocacy-link":
     targetId = "advocacy-section";
     // Ensure environment listeners are cleaned up properly
     cleanupEnvironmentListeners();
     break;
    case "menu-awareness-link":
     targetId = "awareness-section";
     break;
   }

   // Check if we're in engagement content
   const engagementContent = document.getElementById("engagement-content");
   const environmentContent = document.getElementById("environment-content");

   // Hide both engagement and environment content if they're visible
   if (engagementContent) {
    engagementContent.style.display = "none";
   }
   if (environmentContent) {
    environmentContent.style.display = "none";
    environmentContent.classList.remove("active");

    // Use our global cleanup function
    cleanupEnvironmentListeners();
   }

   // Show loyalty content and navigate
   const loyaltyContent = document.getElementById("loyalty-content");
   if (loyaltyContent) {
    loyaltyContent.style.display = "block";

    // Re-enable scrolling
    document.body.style.overflow = "auto";
    document.documentElement.style.overflow = "auto";

    // Remove any fixed positioning that might interfere
    document.body.style.position = "";
    document.documentElement.style.position = "";

    // Wait for content to be visible then scroll
    setTimeout(() => {
     const targetElement = document.getElementById(targetId);
     if (targetElement) {
      // First scroll to top to ensure consistent behavior
      window.scrollTo(0, 0);

      // Then scroll to target section
      setTimeout(() => {
       targetElement.scrollIntoView({ behavior: "smooth" });

       // Reinitialize any necessary animations or functionality
       if (typeof initializeLoyaltyAnimations === "function") {
        initializeLoyaltyAnimations();
       }
      }, 100);
     }
     // Ensure scrolling works after navigation
     cleanupExistingListeners();
    }, 100);
   }

   // Close the menu
   const menuContent = document.querySelector(".menu-content");
   if (menuContent) {
    menuContent.classList.remove("open");
    document.body.classList.remove("menu-open");
   }
  });
 });

 // Handle journey item clicks
 journeyItems.forEach((item) => {
  item.addEventListener("click", () => {
   const step = item.dataset.step;
   const target = item.dataset.target;

   // Clean up any existing wheel event listeners to prevent conflicts
   const cleanupExistingListeners = () => {
    try {
     // Remove any wheel event listeners that might exist
     window.removeEventListener("wheel", handleWheel, { passive: false });
     document.removeEventListener("wheel", handleWheel, { passive: false });

     // Also try removing with different options
     window.removeEventListener("wheel", handleWheel);
     document.removeEventListener("wheel", handleWheel);

     // Remove event listeners from environment and engagement sections
     const environmentSection = document.querySelector(".environment-section");
     if (environmentSection) {
      environmentSection.removeEventListener("wheel", handleWheel, {
       passive: false,
      });
      environmentSection.removeEventListener("wheel", handleWheel);
     }
    } catch (e) {
     /* Ignore errors if listener doesn't exist */
    }

    // Reset scrolling properties on the body and html elements
    document.body.style.overflow = "auto";
    document.documentElement.style.overflow = "auto";
    document.body.style.position = "";
    document.documentElement.style.position = "";

    // Ensure wheel events work normally for all scrolling
    document.addEventListener(
     "wheel",
     function wheelHandler(e) {
      // This is just to ensure normal scrolling behavior is restored
      // The event will propagate naturally
     },
     { passive: true }
    );
   };

   // Run cleanup before proceeding
   cleanupExistingListeners();

   // Handle items with data-target attribute (scroll to sections in loyalty content)
   if (target) {
    // Hide any visible content first
    const engagementContent = document.getElementById("engagement-content");
    const environmentContent = document.getElementById("environment-content");

    if (engagementContent) {
     engagementContent.style.display = "none";
    }
    if (environmentContent) {
     environmentContent.style.display = "none";
     environmentContent.classList.remove("active");
     cleanupEnvironmentListeners();
    }

    // Show loyalty content
    const loyaltyContent = document.getElementById("loyalty-content");
    if (loyaltyContent) {
     loyaltyContent.style.display = "block";

     // Re-enable scrolling
     document.body.style.overflow = "auto";
     document.documentElement.style.overflow = "auto";
     document.body.style.position = "";
     document.documentElement.style.position = "";

     // Wait for content to be visible then scroll to target section
     setTimeout(() => {
      const targetElement = document.getElementById(target);
      if (targetElement) {
       targetElement.scrollIntoView({ behavior: "smooth" });

       // Reinitialize any necessary animations or functionality
       if (typeof initializeLoyaltyAnimations === "function") {
        initializeLoyaltyAnimations();
       }
      }
     }, 100);
    }

    // Close the menu
    const menuContent = document.querySelector(".menu-content");
    if (menuContent) {
     menuContent.classList.remove("open");
     document.body.classList.remove("menu-open");
    }
    return;
   }

   // Handle items with data-step attribute (load engagement content)
   if (step) {
    // Load engagement content
    loadEngagementContent();

    // Create a message listener to wait for the engagement iframe to load
    const handleMessage = (event) => {
     // Only process messages from engagement.html
     if (event.data && event.data.type === "engagementLoaded") {
      // Remove this listener once we've processed the message
      window.removeEventListener("message", handleMessage);

      // Send a message to the iframe to scroll to the selected section
      const iframe = document.querySelector("#engagement-content iframe");
      if (iframe && iframe.contentWindow) {
       iframe.contentWindow.postMessage(
        {
         type: "scrollToSection",
         sectionId: step,
        },
        "*"
       );

       // Run cleanup again after navigation to ensure scrolling works
       setTimeout(cleanupExistingListeners, 500);
      }
     }
    };

    // Add the message listener
    window.addEventListener("message", handleMessage);

    // Close the menu
    const menuContent = document.querySelector(".menu-content");
    if (menuContent) {
     menuContent.classList.remove("open");
     document.body.classList.remove("menu-open");
    }
   }
  });
 });
}

// Function to thoroughly clean up all environment-specific event listeners
function cleanupEnvironmentListeners() {
 console.log("Performing thorough cleanup of environment listeners");
 try {
  // Try to remove any wheel handlers with different combinations
  window.removeEventListener("wheel", handleWheel, { passive: false });
  document.removeEventListener("wheel", handleWheel, { passive: false });
  window.removeEventListener("wheel", handleWheel);
  document.removeEventListener("wheel", handleWheel);

  // Try to access and remove environment-specific handlers using a safe approach
  try {
   // If handleEnvironmentWheel is defined globally
   if (typeof handleEnvironmentWheel === "function") {
    window.removeEventListener("wheel", handleEnvironmentWheel, {
     passive: false,
    });
    document.removeEventListener("wheel", handleEnvironmentWheel, {
     passive: false,
    });
    window.removeEventListener("wheel", handleEnvironmentWheel);
    document.removeEventListener("wheel", handleEnvironmentWheel);
   }

   // Try to remove touch and keyboard handlers
   window.removeEventListener("touchstart", handleTouchStart, {
    passive: false,
   });
   window.removeEventListener("touchmove", handleTouchMove, { passive: false });
   document.removeEventListener("touchstart", handleTouchStart, {
    passive: false,
   });
   document.removeEventListener("touchmove", handleTouchMove, {
    passive: false,
   });
   window.removeEventListener("keydown", handleKeydown);
   document.removeEventListener("keydown", handleKeydown);
  } catch (innerError) {
   console.log("Non-critical error removing specific handlers:", innerError);
  }

  // Also try removing from environment section element directly
  const environmentSection = document.querySelector(".environment-section");
  if (environmentSection) {
   environmentSection.removeEventListener("wheel", handleWheel, {
    passive: false,
   });
   environmentSection.removeEventListener("wheel", handleWheel);
   // Try any other combinations that might exist
   try {
    if (typeof handleEnvironmentWheel === "function") {
     environmentSection.removeEventListener("wheel", handleEnvironmentWheel, {
      passive: false,
     });
     environmentSection.removeEventListener("wheel", handleEnvironmentWheel);
    }
   } catch (sectionError) {
    // Ignore errors
   }
  }

  // Restore normal scrolling properties
  document.body.style.overflow = "auto";
  document.documentElement.style.overflow = "auto";
  document.body.style.position = "";
  document.documentElement.style.position = "";

  // Add a passive wheel handler to restore normal scrolling
  window.addEventListener(
   "wheel",
   function normalScrollHandler(e) {
    // This passive handler ensures normal scrolling behavior
   },
   { passive: true }
  );
 } catch (e) {
  console.log("Error during environment listener cleanup:", e);
 }
}
